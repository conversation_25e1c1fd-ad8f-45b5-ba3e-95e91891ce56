import { Dimensions, StyleSheet, View } from "react-native";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";

import { useRouter } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";

import Body from "@/components/typography/Body";
import Heading from "@/components/typography/Heading";

import Button from "@/components/ui/buttons/Button";
import Progress from "@/components/ui/progress/Progress";

import BulbIcon from "@/components/icons/Bulb";
import BrainIcon from "@/components/icons/Brain";
import CheckmarkIcon from "@/components/icons/Checkmark";
import ChatFolderIcon from "@/components/icons/ChatFolder";
import ExtractionIcon from "@/components/icons/Extraction";

import useNavigationBar from "@/hooks/useNavigationBar";

import { useSlipInfoContext } from "@/contexts/SlipInfoContext";

import {
  FunctionProp,
  ProcessingItem,
  ProcessingItemProps,
} from "@/types/Common";
import { SlipInfoData } from "@/types/SlipInfoContext";

import { handleFetch } from "@/helpers/api";

export default function Analyzing({ onBack }: FunctionProp) {
  const processingSteps: Array<ProcessingItem> = [
    {
      id: 0,
      text: "Extraindo de informações sobre apostas",
      description:
        "Lendo padrões recentes e eventos que impactam diretamente os resultados dos jogos.",
      Icon: ExtractionIcon,
    },
    {
      id: 1,
      text: "Buscando dados estatísticos",
      description:
        "Reunindo estatísticas detalhadas das equipes, forma recente e confrontos diretos.",
      Icon: ChatFolderIcon,
    },
    {
      id: 2,
      text: "Correndo modelo de análise IA",
      description:
        "Aplicando inteligência artificial para identificar padrões ocultos e prever cenários prováveis.",
      Icon: BrainIcon,
    },
    {
      id: 3,
      text: "Gerando análise e recomendações",
      description:
        "Combinando probabilidades, valores de apostas e histórico para gerar uma análise fundamentada.",
      Icon: BulbIcon,
    },
    {
      id: 4,
      text: "Análise completada",
      description:
        "A análise detalhada foi concluída com sucesso e está pronta para consulta.",
      Icon: CheckmarkIcon,
    },
  ];
  const progressInterval = (5 * 1000) / 100; // 5 seconds in ms divided by 100%
  const checkStatusInterval = 0.5 * 1000;

  const [activeStep, setActiveStep] = useState(0);
  const [stepsProgress, setStepsProgress] = useState<Array<number>>(
    Array.from<number>({ length: processingSteps.length }).fill(0)
  );

  const translateX = useSharedValue(-52); // half of big circle
  const listItemsGap = useMemo(() => {
    const contentWidth = Dimensions.get("window").width - 16 * 2 - 24 * 2; // window width - margin - padding
    const availableSpace = contentWidth - 104 - 60 * 2; // content width - big circle - 2 x small circle
    const gap = availableSpace / 2;
    const gapWithMargin = gap - gap * 0.05;

    return gapWithMargin;
  }, []);

  const progressRef = useRef<NodeJS.Timeout>();
  const checkStatusRef = useRef<NodeJS.Timeout>();
  const statusRef = useRef<boolean>(false);

  const router = useRouter();

  const { height } = useNavigationBar();
  const { data, setData } = useSlipInfoContext();

  const navigate = () => {
    onBack();
    router.push("/analysis");
  };

  const onAnimationEnd = (finished?: boolean) => {
    if (!finished) return;

    const syncedActiveStep = activeStep + 1; // This events occurs before the setState rerender, so we need to count +1

    if (syncedActiveStep < processingSteps.length - 1) return;

    runOnJS(navigate)();
  };

  const handleProgress = () => {
    setStepsProgress((prev) => {
      const arrayClone = [...prev];
      const progress = arrayClone[activeStep] + 1;
      const stopProgress =
        !statusRef.current && activeStep + 1 === processingSteps.length - 1
          ? -1
          : 100;

      if (progress >= stopProgress && stopProgress !== -1) {
        clearInterval(progressRef.current);

        if (stopProgress === 100) {
          setActiveStep((prev) => {
            const step = prev + 1;

            if (step === processingSteps.length) return prev;

            translateX.value = withTiming(
              -52 - (60 + listItemsGap) * step, // half big circle - (small circle + gap) * step
              undefined,
              onAnimationEnd
            );

            return step;
          });
        }
      }

      arrayClone[activeStep] = progress > 100 ? 0 : progress;

      return arrayClone;
    });
  };

  const handleCheckStatus = async () => {
    const apiRes: SlipInfoData = await handleFetch({
      path: `slips/${data?.id}`,
    });

    if (apiRes?.processing_status > 5) {
      clearInterval(checkStatusRef.current);
      clearInterval(progressRef.current);

      // Some error
      return;
    }

    if (!apiRes?.analysis) return;

    setData((prev) => ({ ...prev, ...apiRes }));
    statusRef.current = true;

    clearInterval(checkStatusRef.current);
    clearInterval(progressRef.current);
    progressRef.current = setInterval(handleProgress, progressInterval / 10);
  };

  useEffect(() => {
    progressRef.current = setInterval(handleProgress, progressInterval);

    return () => {
      clearInterval(checkStatusRef.current);
      clearInterval(progressRef.current);
    };
  }, []);

  useEffect(() => {
    if (!statusRef.current) {
      clearInterval(checkStatusRef.current);
      checkStatusRef.current = setInterval(
        handleCheckStatus,
        checkStatusInterval
      );
    }

    if (activeStep === 0 || activeStep === processingSteps.length - 1) return;

    progressRef.current = setInterval(
      handleProgress,
      progressInterval / (statusRef.current ? 10 : 1)
    );

    return () => {
      clearInterval(checkStatusRef.current);
      clearInterval(progressRef.current);
    };
  }, [activeStep]);

  const transformAnimation = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],
    };
  });

  return (
    <View style={[styles.content, { marginBottom: height }]}>
      <View style={styles.processing}>
        <Animated.View
          style={[
            styles.processingSteps,
            transformAnimation,
            {
              gap: listItemsGap,
            },
          ]}
        >
          {processingSteps.map((step, id) => (
            <StepItem
              key={`analyzing_step_${id}`}
              {...step}
              activeStep={activeStep}
              progress={stepsProgress[id]}
              processLength={processingSteps.length}
            />
          ))}
        </Animated.View>
        <LinearGradient
          colors={["#171728", "#FFFFFF00", "#FFFFFF00", "#171728"]}
          locations={[0.05, 0.35, 0.65, 0.95]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradient}
        />
      </View>
      <View style={styles.description}>
        <Heading variant="300" align="center">
          {processingSteps[activeStep].text}
        </Heading>
        <Body color="secondary" align="center">
          {processingSteps[activeStep].description}
        </Body>
      </View>
      <Button variant="secondary" withIcon={false} onPress={onBack}>
        <Body highlight>Voltar</Body>
      </Button>
    </View>
  );
}

function StepItem({
  id,
  Icon,
  activeStep,
  progress,
  processLength,
}: ProcessingItemProps) {
  const isCompleted = id === processLength - 1;
  const isStepActive = activeStep === id;
  const isPrevStep = activeStep > id;
  const opacity = useSharedValue(isStepActive ? 0 : 1);
  const rotate = useSharedValue(isStepActive ? 0 : isPrevStep ? -15 : 15);
  const size = useSharedValue(isStepActive ? 104 : 60);

  const animate = useCallback(() => {
    if (isStepActive) {
      opacity.value = withTiming(0);
      rotate.value = withTiming(0);
      size.value = withTiming(104);
      return;
    }

    opacity.value = withTiming(1);
    rotate.value = withTiming(isPrevStep ? -15 : 15);
    size.value = withTiming(60);
  }, [activeStep]);

  useEffect(() => {
    animate();
  }, [activeStep]);

  const positioningAnimations = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${rotate.value}deg` }],
      width: size.value,
      height: size.value,
    };
  });

  const inactiveAnimation = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
    };
  });

  return (
    <Animated.View
      key={`processing_step_${id}`}
      style={[
        styles.processingItem,
        positioningAnimations,
        { backgroundColor: isCompleted ? "#0EA64B1F" : undefined },
      ]}
    >
      {!isCompleted && <Progress progress={progress} />}
      <Icon
        style={styles.processingIcon}
        size={40}
        color={isCompleted ? "#0EA64B" : "gradient"}
      />
      <Animated.View style={[styles.inactiveLayout, inactiveAnimation]}>
        <Icon color={activeStep > id ? "gradient" : "#454559"} />
      </Animated.View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  content: {
    display: "flex",
    backgroundColor: "#171728",
    gap: 24,
    paddingTop: 40,
    paddingBottom: 24,
    paddingHorizontal: 24,
    marginHorizontal: 16,
    borderRadius: 20,
  },
  processing: {
    position: "relative",
    overflow: "hidden",
  },
  processingSteps: {
    display: "flex",
    flexDirection: "row",
    alignItems: "flex-end",
    marginLeft: "50%",
    minHeight: 104,
  },
  processingItem: {
    position: "relative",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: "100%",
    overflow: "hidden",
  },
  inactiveLayout: {
    position: "absolute",
    display: "flex",
    width: "100%",
    height: "100%",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#1E1E30",
    top: 0,
    left: 0,
  },
  gradient: {
    position: "absolute",
    width: "100%",
    height: "100%",
    top: 0,
    left: 0,
  },
  processingIcon: {
    position: "absolute",
  },
  description: {
    display: "flex",
    gap: 8,
  },
});
