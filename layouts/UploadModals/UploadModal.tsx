import { useEffect, useState } from "react";
import { Image, StyleSheet, View } from "react-native";

import { ShareIntentFile, useShareIntentContext } from "expo-share-intent";
import { DocumentPickerAsset, getDocumentAsync } from "expo-document-picker";
import { ImagePickerAsset, launchImageLibraryAsync } from "expo-image-picker";

import DropdownListItem from "@/components/ui/dropdown/ListItem";
import InlineNotification from "@/components/ui/notifications/InlineNotification";

import ImageIcon from "@/components/icons/Image";
import DocumentIcon from "@/components/icons/Document";

import { useSlipInfoContext } from "@/contexts/SlipInfoContext";

import useNavigationBar from "@/hooks/useNavigationBar";

import { ModalUploadProps } from "@/types/Modal";

import { handleFetch } from "@/helpers/api";

export default function UploadModal({ onUploadChange }: ModalUploadProps) {
  const [loading, setLoading] = useState(false);

  const { setData } = useSlipInfoContext();
  const { height } = useNavigationBar();

  const getImageDimensions = async (
    uri: string
  ): Promise<Pick<ImagePickerAsset, "width" | "height">> => {
    return new Promise((resolve) => {
      Image.getSize(
        uri,
        (width, height) => {
          resolve({ width, height });
        },
        () => {
          onUploadChange("error", true);
        }
      );
    });
  };

  const { shareIntent, resetShareIntent } = useShareIntentContext();

  const handleAsset = async (
    asset: ImagePickerAsset | DocumentPickerAsset | ShareIntentFile | undefined,
    assetSize: number | null | undefined,
    canceled: boolean
  ) => {
    const exceedsMaxSize = assetSize && assetSize > 5e6; // 5e6 = 5MB

    const allowedTypes = ["image/jpeg", "image/png", "image/gif"];
    const invalidType = !allowedTypes.includes(asset?.mimeType || "");

    if (canceled) return;

    if (!asset || exceedsMaxSize || invalidType) {
      onUploadChange("error", true);
      return;
    }

    const assetUri = "path" in asset ? asset.path : asset?.uri;
    const assetName = "name" in asset ? asset.name : asset.fileName;
    const formData = new FormData();

    formData.append("uploaded_image", {
      uri: assetUri,
      name: assetName,
      type: asset.mimeType,
    } as any);

    setLoading(true);

    const apiRes = await handleFetch({
      path: "slips/actions/submit/",
      method: "POST",
      body: formData,
      isMultipart: true,
    });

    setLoading(false);

    if (!apiRes.ok) {
      onUploadChange("error", true);
      return;
    }

    const dimensions = await getImageDimensions(assetUri);

    setData((prev) => ({
      ...prev,
      ...apiRes,
      image: { ...asset, ...dimensions, uri: assetUri },
    }));
    onUploadChange("analysis");
  };

  useEffect(() => {
    if (!shareIntent.files?.length) return;

    const file = shareIntent.files[0];

    resetShareIntent();
    handleAsset(file, file.size, false);
  }, [shareIntent]);

  const handleImageUpload = async () => {
    const result = await launchImageLibraryAsync();

    const image = result.assets?.[0];

    handleAsset(image, image?.fileSize, result.canceled);
  };

  const handleFileUpload = async () => {
    const result = await getDocumentAsync({
      type: ["image/jpeg", "image/png", "image/gif"],
    });

    const file = result.assets?.[0];

    handleAsset(file, file?.size, result.canceled);
  };

  const options = [
    {
      label: "Carregue uma imagem",
      icon: <ImageIcon color="#454559" />,
      onPress: handleImageUpload,
    },
    {
      label: "Procurar arquivos",
      icon: <DocumentIcon color="#454559" />,
      onPress: handleFileUpload,
    },
  ];

  return (
    <View style={[styles.container, { paddingBottom: 16 + height }]}>
      <InlineNotification description="Apenas jogos futuros são analisados, com no máximo 3 apostas por bilhete" />
      <View style={styles.options}>
        <DropdownListItem options={options} loading={loading} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    display: "flex",
    backgroundColor: "#121221",
    gap: 16,
    paddingTop: 24,
    paddingRight: 16,
    paddingLeft: 16,
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
  },
  options: {
    display: "flex",
    gap: 8,
  },
});
