import { StyleSheet, View } from "react-native";

import ErrorContent from "./ErrorContent";
import AnalysisContent from "./AnalysisContent";

import useNavigationBar from "@/hooks/useNavigationBar";

import { ModalProcessingProps } from "@/types/Modal";

export default function ProcessingModal({
  error,
  onUploadChange,
}: ModalProcessingProps) {
  const { height } = useNavigationBar();

  return (
    <View style={[styles.uploadProcessing, { marginBottom: height }]}>
      {error ? (
        <ErrorContent error={error} onChange={onUploadChange} />
      ) : (
        <AnalysisContent onChange={onUploadChange} />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  uploadProcessing: {
    display: "flex",
    alignItems: "center",
    backgroundColor: "#121221",
    gap: 24,
    paddingTop: 40,
    paddingHorizontal: 24,
    paddingBottom: 24,
    marginHorizontal: 16,
    borderRadius: 20,
  },
});
