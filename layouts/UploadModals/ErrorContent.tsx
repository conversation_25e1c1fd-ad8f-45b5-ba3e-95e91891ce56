import { Fragment, useMemo } from "react";
import { StyleSheet, View } from "react-native";

import Button from "@/components/ui/buttons/Button";

import Body from "@/components/typography/Body";
import Heading from "@/components/typography/Heading";

import ErrorIcon from "@/components/icons/Error";
import RoundArrow from "@/components/icons/RoundArrow";

import { ModalContentProps } from "@/types/Modal";

export default function ErrorContent({ error, onChange }: ModalContentProps) {
  const { title, description } = useMemo(() => {
    switch (error) {
      case 501: {
        return {
          title: "Mais de 3 simples:",
          description:
            "Seu boletim tem mais de 3 bets simples. Por favor corrija.",
        };
      }
      case 500: {
        return {
          title: "Mais de 3 múltiplas:",
          description:
            "Seu boletim tem mais de 3 bets múltiplas. Por favor corrija.",
        };
      }
      default: {
        return {
          title: "Imagem incorreta:",
          description:
            "Não conseguimos identificar um boletim de apostas na imagem. Verifique se está nítida e se mostra todas as informações.",
        };
      }
    }
  }, [error]);

  return (
    <Fragment>
      <View style={styles.errorIcon}>
        <ErrorIcon size={40} color="#F24A4C" />
      </View>
      <View style={styles.message}>
        <Heading style={styles.heading} variant="300" align="center">
          {title}
        </Heading>
        <Body color="secondary" align="center">
          {description}
        </Body>
      </View>
      <Button
        variant="secondary"
        icon={<RoundArrow />}
        onPress={() => onChange("upload")}
      >
        <Body>Tentar novamente</Body>
      </Button>
    </Fragment>
  );
}

const styles = StyleSheet.create({
  errorIcon: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#F03E3E0F",
    width: 104,
    height: 104,
    borderRadius: 100,
  },
  message: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    textAlign: "center",
    gap: 8,
  },
  heading: {
    marginHorizontal: 10,
  },
});
