import { StyleSheet, View } from "react-native";
import { Fragment, useEffect, useRef, useState } from "react";

import { useRouter } from "expo-router";
import { useShareIntentContext } from "expo-share-intent";

import Body from "@/components/typography/Body";
import Heading from "@/components/typography/Heading";

import Button from "@/components/ui/buttons/Button";
import Progress from "@/components/ui/progress/Progress";

import { useSlipInfoContext } from "@/contexts/SlipInfoContext";

import { ModalContentProps } from "@/types/Modal";
import { SlipInfoData } from "@/types/SlipInfoContext";

import { handleFetch } from "@/helpers/api";

export default function AnalysisContent({ onChange }: ModalContentProps) {
  const progressInterval = (5 * 1000) / 100; // 5 seconds in ms divided by 100%
  const checkStatusInterval = 0.5 * 1000;

  const [progress, setProgress] = useState(0);

  const progressRef = useRef<NodeJS.Timeout>();
  const checkStatusRef = useRef<NodeJS.Timeout>();
  const statusRef = useRef<boolean>(false);

  const router = useRouter();

  const { data, setData } = useSlipInfoContext();
  const { resetShareIntent } = useShareIntentContext();

  const handleProgress = () => {
    setProgress((prev) => {
      const stopProgress = statusRef.current ? 100 : 99;

      if (prev === stopProgress) {
        clearInterval(progressRef.current);

        return prev;
      }

      return (prev += 1);
    });
  };

  const handleCheckStatus = async () => {
    const apiRes: SlipInfoData = await handleFetch({
      path: `slips/${data?.id}`,
    });

    if (apiRes?.processing_status > 5) {
      clearInterval(checkStatusRef.current);
      clearInterval(progressRef.current);
      onChange("error", apiRes?.processing_status);
      return;
    }

    if (!apiRes?.fixtures_with_bets?.length) return;

    setData((prev) => ({ ...prev, ...apiRes }));
    statusRef.current = true;

    clearInterval(checkStatusRef.current);
    clearInterval(progressRef.current);
    progressRef.current = setInterval(handleProgress, progressInterval / 10);
  };

  useEffect(() => {
    if (progress > 0) return;

    progressRef.current = setInterval(handleProgress, progressInterval);
    checkStatusRef.current = setInterval(
      handleCheckStatus,
      checkStatusInterval
    );

    return () => {
      clearInterval(progressRef.current);
      clearInterval(checkStatusRef.current);
    };
  }, []);

  useEffect(() => {
    if (progress !== 100) return;

    router.push("/processed");
  }, [progress]);

  const handleBackBtn = () => {
    resetShareIntent();
    onChange("upload");
  };

  return (
    <Fragment>
      <View style={styles.progress}>
        <Progress progress={progress} />
        <Heading style={styles.progressText} variant="400">
          {progress}%
        </Heading>
      </View>
      <View style={styles.message}>
        <Heading style={styles.heading} variant="300" align="center">
          Lendo seu boletim de apostas...
        </Heading>
        <Body color="secondary" align="center">
          Nossa IA está analisando seu boletim de apostas
        </Body>
      </View>
      <Button variant="secondary" withIcon={false} onPress={handleBackBtn}>
        <Body highlight>Voltar</Body>
      </Button>
    </Fragment>
  );
}

const styles = StyleSheet.create({
  progress: {
    position: "relative",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  progressText: {
    position: "absolute",
  },
  message: {
    display: "flex",
    alignItems: "center",
    gap: 8,
  },
  heading: {
    marginHorizontal: 10,
  },
});
