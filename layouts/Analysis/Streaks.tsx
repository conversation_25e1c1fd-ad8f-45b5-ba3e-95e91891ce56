import { useEffect } from "react";
import { StyleSheet, Text, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";

import Body from "@/components/typography/Body";
import Heading from "@/components/typography/Heading";

import VerticalList from "@/components/ui/common/VerticalList";

import FlashIcon from "@/components/icons/Flash";

import { SlipBetsInfo, SlipFixturesAnalysis } from "@/types/SlipInfoContext";
import { AnalysisComponentProps, ItemProp, ListItemProp } from "@/types/Common";

import StreaksSkeleton from "./Skeletons/StreaksSkeleton";

export default function AnalysisStreaks({
  scrollGesture,
  // analysis,
  fixtures,
  loading,
}: AnalysisComponentProps) {
   const analysis = [
      {
        key_analysis: [],
        relevant_streaks: [
          "SSV Ulm teve vantagem com 3 vitórias nos últimos 6 confrontos diretos contra o Darmstadt.",
          "Darmstadt 98 perdeu os últimos 4 jogos como visitante na 2. Bundesliga.",
          "Darmstadt 98 não venceu nenhum dos últimos 8 jogos como visitante.",
        ],
        home_team_form: [1, 1, 2, 2, 1],
        away_team_form: [3, 1, 3, 3, 1],
        historic_h2h_fixtures: [
          {
            home_goals: 1,
            away_goals: 1,
            home_team: "SV Darmstadt 98",
            away_team: "SSV Ulm 1846",
            date: "2024-10-27",
          },
          {
            home_goals: 3,
            away_goals: 0,
            home_team: "SSV Ulm 1846",
            away_team: "SV Darmstadt 98",
            date: "2010-09-22",
          },
          {
            home_goals: 1,
            away_goals: 1,
            home_team: "SSV Ulm 1846",
            away_team: "SV Darmstadt 98",
            date: "2010-04-17",
          },
          {
            home_goals: 3,
            away_goals: 1,
            home_team: "SV Darmstadt 98",
            away_team: "SSV Ulm 1846",
            date: "2009-10-02",
          },
          {
            home_goals: 0,
            away_goals: 4,
            home_team: "SV Darmstadt 98",
            away_team: "SSV Ulm 1846",
            date: "2009-03-07",
          },
        ],
        fixture_with_bet_id: "06879e9d-3519-478c-98d4-b23e90f78606",
      },
      {
        key_analysis: [],
        relevant_streaks: [
          "Konyaspor venceu 3 dos últimos 5 jogos em casa.",
          "Konyaspor venceu 2 jogos consecutivos em casa.",
        ],
        home_team_form: [1, 3, 1, 2, 3],
        away_team_form: [1, 1, 1, 3, 1],
        historic_h2h_fixtures: [
          {
            home_goals: 1,
            away_goals: 0,
            home_team: "Konyaspor",
            away_team: "Gaziantep FK",
            date: "2025-03-28",
          },
          {
            home_goals: 3,
            away_goals: 1,
            home_team: "Gaziantep FK",
            away_team: "Konyaspor",
            date: "2024-10-27",
          },
        ],
        fixture_with_bet_id: "92be90fc-0862-44d2-84bf-5080d7bb5785",
      },
      {
        key_analysis: [],
        relevant_streaks: [],
        home_team_form: [],
        away_team_form: [],
        historic_h2h_fixtures: [
          {
            home_goals: 2,
            away_goals: 2,
            home_team: "SCR Altach",
            away_team: "Linzer ASK",
            date: "2025-03-28",
          },
        ],
        fixture_with_bet_id: "f72b9d96-b235-4db5-a23d-561d6be7e7fa",
      },
    ];

  const hasStreaks = analysis?.some((item) => item.relevant_streaks.length > 0);

  if (loading) return <StreaksSkeleton />;

  if (!hasStreaks) return null;

  return (
    <View style={styles.container}>
      <Heading variant="200">Sequências relevantes</Heading>
      <VerticalList
        dataLength={analysis?.length || 0}
        type="streaks"
        scrollGesture={scrollGesture}
      >
        {({ hasPagination, activeIndex }) => (
          <View style={styles.streaks}>
            {analysis?.map((item, id) => {
              const isActive = activeIndex === id;

              return (
                <StreaksList
                  key={`streaks_item_${id}`}
                  data={{ ...item, ...fixtures[id] }}
                  hasPagination={hasPagination}
                  isActive={isActive}
                />
              );
            })}
          </View>
        )}
      </VerticalList>
    </View>
  );
}

function StreaksList({
  data,
  hasPagination,
  isActive,
}: ListItemProp<SlipFixturesAnalysis & SlipBetsInfo>) {
  const opacity = useSharedValue(isActive ? 1 : 0.12);

  const marketTextSplitted = data.bet_market_text.split(" - ");

  useEffect(() => {
    opacity.value = withTiming(isActive ? 1 : 0.12);
  }, [isActive]);

  const animatedStyles = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  return (
    <Animated.View
      style={[
        styles.item,
        {
          minWidth: hasPagination ? 300 : undefined,
        },
        animatedStyles,
      ]}
    >
      {hasPagination && (
        <View>
          <Text numberOfLines={1}>
            <Body highlight>{data.fixture_home_team_name} </Body>
            <Body color="secondary">vs </Body>
            <Body highlight>{data.fixture_away_team_name} FK</Body>
          </Text>
          <Text numberOfLines={1}>
            <Body color="secondary">{marketTextSplitted[0]}: </Body>
            <Body>{marketTextSplitted[1]} </Body>
            <Body color="primary" highlight>
              {Number(data.bet_odd).toFixed(2)}
            </Body>
          </Text>
        </View>
      )}
      {data.relevant_streaks.map((info, id) => (
        <StreakItem
          key={`streak_${data.fixture_home_team_name}_${data.fixture_away_team_name}_${id}`}
          info={info}
        />
      ))}
    </Animated.View>
  );
}

function StreakItem({ info }: ItemProp) {
  return (
    <View style={styles.streak}>
      <FlashIcon color="#F8E541" />
      <Body style={styles.streakText}>{info}</Body>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#121221",
    padding: 16,
    gap: 16,
    marginTop: 8,
  },
  streaks: {
    flexDirection: "row",

    gap: 16,
  },
  item: {
    flex: 1,
    gap: 16,
  },
  streak: {
    maxWidth: "100%",
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  streakText: {
    flex: 1,
  },
});
