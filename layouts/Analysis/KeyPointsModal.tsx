import { StyleSheet, Text, View } from "react-native";

import Body from "@/components/typography/Body";
import Heading from "@/components/typography/Heading";

import SpeedIcon from "@/components/icons/Speed";
import Speed5Icon from "@/components/icons/Speed5";
import DiamondIcon from "@/components/icons/Diamond";

import SheetHandleModal from "@/components/ui/modals/SheetHandleModal";

import { ModalProps } from "@/types/Modal";

export default function KeyPointsModal({
  visible,
  onClose,
}: Pick<ModalProps, "visible" | "onClose">) {
  const sections = [
    {
      Icon: SpeedIcon,
      title: "Grau de confiança",
      description:
        "Indica o nível de confiança na aposta com base em estatísticas, desempenho recente, padrões históricos e diversos outros fatores, como: escalações, clima, estilo de arbitragem, lesões, moral do time, fadiga acumulada e muito mais",
    },
    {
      Icon: DiamondIcon,
      title: "Probabilidade",
      description:
        "A probabilidade estimada do evento ocorrer, com base em nossos dados, comparada à probabilidade oferecida pela casa de apostas",
    },
    {
      Icon: Speed5Icon,
      title: "Valor esperado",
      description:
        "Diferença entre a probabilidade real do evento acontecer e probabilidade da casa de apostas",
    },
  ];

  return (
    <SheetHandleModal visible={visible} onClose={onClose}>
      <View style={styles.content}>
        {sections.map(({ Icon, title, description }, id) => (
          <View key={`key_points_modal_section_${id}`} style={styles.section}>
            <Icon color="gradient" />
            <View style={styles.sectionInfo}>
              <Heading variant="200">{title}</Heading>
              <Body>{description}</Body>
            </View>
          </View>
        ))}
      </View>
    </SheetHandleModal>
  );
}

const styles = StyleSheet.create({
  content: {
    width: "100%",
    alignItems: "center",
    marginTop: 12,
    paddingHorizontal: 16,
    paddingBottom: 8,
    gap: 24,
  },
  section: {
    gap: 8,
  },
  sectionInfo: {
    gap: 4,
  },
});
