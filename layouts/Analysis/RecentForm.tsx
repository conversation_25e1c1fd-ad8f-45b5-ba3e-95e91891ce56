import { useEffect } from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";

import Body from "@/components/typography/Body";
import Heading from "@/components/typography/Heading";
import Captions from "@/components/typography/Captions";

import Form from "@/components/ui/common/Form";
import VerticalList from "@/components/ui/common/VerticalList";

import {
  AnalysisComponentProps,
  FormType,
  ListItemProp,
  RecentFormItemProp,
} from "@/types/Common";
import { SlipBetsInfo, SlipFixturesAnalysis } from "@/types/SlipInfoContext";

import RecentFormSkeleton from "./Skeletons/RecentFormSkeleton";

export default function AnalysisRecentForm({
  scrollGesture,
  analysis,
  fixtures,
  loading,
}: AnalysisComponentProps) {
  const hasRecentForm = analysis?.some(
    (item) => item.home_team_form.length > 0 || item.away_team_form.length > 0
  );

  if (loading) return <RecentFormSkeleton />;

  if (!hasRecentForm) return null;

  return (
    <View style={styles.container}>
      <Heading variant="200">Forma recente</Heading>
      <VerticalList
        dataLength={analysis?.length || 0}
        type="recentForm"
        scrollGesture={scrollGesture}
      >
        {({ hasPagination, activeIndex }) => (
          <View style={styles.recentFormList}>
            {analysis?.map((item, id) => {
              const isActive = activeIndex === id;

              return (
                <RecentFormList
                  key={`recent_form_item_${id}`}
                  data={{ ...item, ...fixtures[id] }}
                  hasPagination={hasPagination}
                  isActive={isActive}
                />
              );
            })}
          </View>
        )}
      </VerticalList>
    </View>
  );
}

function RecentFormList({
  data,
  hasPagination,
  isActive,
}: ListItemProp<SlipFixturesAnalysis & SlipBetsInfo>) {
  const opacity = useSharedValue(isActive ? 1 : 0.12);

  useEffect(() => {
    opacity.value = withTiming(isActive ? 1 : 0.12);
  }, [isActive]);

  const animatedStyles = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  return (
    <Animated.View
      style={[
        styles.item,
        {
          minWidth: hasPagination ? 300 : undefined,
        },
        animatedStyles,
      ]}
    >
      <RecentFormItem
        key={`recent_form_item_${data.fixture_home_team_name}_${data.fixture_away_team_name}`}
        homeTeam={data.fixture_home_team_name}
        homeTeamForm={data.home_team_form}
        awayTeam={data.fixture_away_team_name}
        awayTeamForm={data.away_team_form}
      />
    </Animated.View>
  );
}

function RecentFormItem({
  homeTeam,
  homeTeamForm,
  awayTeam,
  awayTeamForm,
}: RecentFormItemProp) {
  const homeTeamFormArray: Array<FormType> = [...homeTeamForm, "upcoming"];
  const awayTeamFormArray: Array<FormType> = [...awayTeamForm, "upcoming"];

  return (
    <View style={styles.recentForm}>
      <View style={styles.teamCard}>
        <Body>{homeTeam}</Body>
        <View style={styles.formList}>
          {homeTeamFormArray.map((form, id) => (
            <Form key={`home_team_${homeTeam}_form_${id}`} type={form} />
          ))}
        </View>
      </View>
      <View style={styles.teamCard}>
        <Body>{awayTeam}</Body>
        <View style={styles.formList}>
          {awayTeamFormArray.map((form, id) => (
            <Form key={`away_team_${awayTeam}_form_${id}`} type={form} />
          ))}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#121221",
    padding: 16,
    gap: 16,
    marginTop: 8,
  },
  recentFormList: {
    flexDirection: "row",
    gap: 16,
  },
  item: {
    flex: 1,
    gap: 16,
  },
  itemHeading: {
    flexDirection: "row",
    gap: 6,
  },
  recentForm: {
    maxWidth: "100%",
    flexDirection: "row",
    alignItems: "center",
    overflow: "hidden",
    gap: 1,
    borderRadius: 8,
  },
  formList: {
    flexDirection: "row",
    gap: 2,
  },
  teamCard: {
    alignItems: "center",
    backgroundColor: "#171728",
    flexShrink: 1,
    flexGrow: 1,
    paddingVertical: 10,
    paddingHorizontal: 8,
    gap: 8,
  },
});
