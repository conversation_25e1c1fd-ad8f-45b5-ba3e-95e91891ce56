import { useEffect } from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";

import Body from "@/components/typography/Body";
import Heading from "@/components/typography/Heading";
import Captions from "@/components/typography/Captions";

import VerticalList from "@/components/ui/common/VerticalList";

import {
  AnalysisComponentProps,
  H2HItemProp,
  ListItemProp,
} from "@/types/Common";
import { SlipBetsInfo, SlipFixturesAnalysis } from "@/types/SlipInfoContext";

import H2HSkeleton from "./Skeletons/H2HSkeleton";

export default function AnalysisH2H({
  scrollGesture,
  analysis,
  fixtures,
  loading,
}: AnalysisComponentProps) {
  const hasH2H = analysis?.some(
    (item) => item.historic_h2h_fixtures.length > 0
  );

  if (loading) return <H2HSkeleton />;

  if (!hasH2H) return null;

  return (
    <View style={styles.container}>
      <Heading variant="200">Confronto direto</Heading>
      <VerticalList
        dataLength={analysis?.length || 0}
        type="H2H"
        scrollGesture={scrollGesture}
      >
        {({ hasPagination, activeIndex }) => (
          <View style={styles.H2HList}>
            {analysis?.map((item, id) => {
              const isActive = activeIndex === id;

              return (
                <H2HList
                  key={`H2H_item_${id}`}
                  data={{ ...item, ...fixtures[id] }}
                  hasPagination={hasPagination}
                  isActive={isActive}
                />
              );
            })}
          </View>
        )}
      </VerticalList>
    </View>
  );
}

function H2HList({
  data,
  hasPagination,
  isActive,
}: ListItemProp<SlipFixturesAnalysis & SlipBetsInfo>) {
  const opacity = useSharedValue(isActive ? 1 : 0.12);

  useEffect(() => {
    opacity.value = withTiming(isActive ? 1 : 0.12);
  }, [isActive]);

  const animatedStyles = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  return (
    <Animated.View
      style={[
        styles.item,
        {
          minWidth: hasPagination ? 300 : undefined,
        },
        animatedStyles,
      ]}
    >
      {hasPagination && (
        <View style={styles.itemHeading}>
          <Body highlight>{data.fixture_home_team_name}</Body>
          <Body color="secondary">vs</Body>
          <Body highlight>{data.fixture_away_team_name} FK</Body>
        </View>
      )}
      <H2HItem
        key={`H2H_${data.fixture_home_team_name}_${data.fixture_away_team_name}`}
        matches={data.historic_h2h_fixtures}
      />
    </Animated.View>
  );
}

function H2HItem({ matches }: H2HItemProp) {
  return (
    <View style={styles.H2HItem}>
      {matches.map(
        ({ home_team, away_team, home_goals, away_goals, date }, id) => {
          const isDraw =
            home_goals === null ||
            away_goals === null ||
            home_goals === away_goals;
          const winner = !isDraw && (home_goals > away_goals ? "home" : "away");
          const formattedDate = date.slice(2).split("-").reverse().join("/");

          return (
            <View
              key={`H2H_match_${home_team}_${away_team}_${id}`}
              style={styles.matchCard}
            >
              <View style={styles.teamsName}>
                <Body
                  color={winner === "home" || !winner ? "white" : "secondary"}
                >
                  {home_team}
                </Body>
                <Body
                  style={styles.awayTeamName}
                  color={winner === "away" || !winner ? "white" : "secondary"}
                >
                  {away_team}
                </Body>
              </View>
              <View style={styles.score}>
                <Body
                  style={styles.homeTeamGoals}
                  color={winner === "home" || !winner ? "white" : "secondary"}
                >
                  {home_goals}
                </Body>
                <Body
                  style={styles.awayTeamGoals}
                  color={winner === "away" || !winner ? "white" : "secondary"}
                >
                  {away_goals}
                </Body>
              </View>
              <View style={styles.separator} />
              <Captions
                style={styles.date}
                variant="200"
                color="disabled"
                align="right"
              >
                {formattedDate}
              </Captions>
            </View>
          );
        }
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#121221",
    padding: 16,
    gap: 16,
    marginTop: 8,
  },
  H2HList: {
    flexDirection: "row",
    gap: 16,
  },
  item: {
    flex: 1,
    gap: 16,
  },
  itemHeading: {
    flexDirection: "row",
    gap: 6,
  },
  H2HItem: {
    overflow: "hidden",
    borderRadius: 8,
    gap: 2,
  },
  matchCard: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#171728",
    paddingVertical: 5,
    paddingHorizontal: 12,
    gap: 8,
  },
  teamsName: {
    flexGrow: 1,
  },
  awayTeamName: {
    marginTop: -2,
  },
  score: {
    alignItems: "center",
  },
  homeTeamGoals: {
    marginTop: 1,
  },
  awayTeamGoals: {
    marginTop: -4,
  },
  separator: {
    width: 1,
    height: "100%",
    backgroundColor: "#1E1E30",
  },
  date: {
    minWidth: 60,
  },
});
