import { Fragment, useEffect, useMemo, useState } from "react";
import { Pressable, StyleSheet, Text, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";

import KeyPointsModal from "./KeyPointsModal";

import Body from "@/components/typography/Body";
import Heading from "@/components/typography/Heading";

import VerticalList from "@/components/ui/common/VerticalList";

import ArrowLeftIcon from "@/components/icons/ArrowLeft";
import InfoIcon from "@/components/icons/Info";

import { SlipBetsInfo, SlipFixturesAnalysis } from "@/types/SlipInfoContext";
import { AnalysisComponentProps, ItemProp, ListItemProp } from "@/types/Common";

import KeyPointsSkeleton from "./Skeletons/KeyPointsSkeleton";

export default function AnalysisKeyPoints({
  scrollGesture,
  analysis,
  fixtures,
  loading,
}: AnalysisComponentProps) {
  const [showInfo, setShowInfo] = useState(false);

  const hasKeyPoints = analysis?.some((item) => item.key_analysis.length > 0);

  const handleModalOpen = () => {
    setShowInfo(true);
  };

  const handleModalClose = () => {
    setShowInfo(false);
  };

  if (loading) return <KeyPointsSkeleton />;

  if (!hasKeyPoints) return null;

  const filteredAnalysis = analysis?.filter(
    (item) => item.key_analysis.length > 0
  );

  return (
    <Fragment>
      <View style={styles.container}>
        <View style={styles.heading}>
          <Heading variant="200">Pontos-chave da Análise</Heading>
          <Pressable onPress={handleModalOpen}>
            <InfoIcon />
          </Pressable>
        </View>
        <VerticalList
          dataLength={filteredAnalysis?.length || 0}
          type="keyPoints"
          scrollGesture={scrollGesture}
        >
          {({ hasPagination, activeIndex }) => {
            return (
              <View style={styles.keyPoints}>
                {filteredAnalysis?.map((item, id) => {
                  const isActive = activeIndex === id;

                  return (
                    <KeyPointsList
                      key={`key_points_item_${id}`}
                      data={item}
                      fixtures={fixtures}
                      hasPagination={hasPagination}
                      hasMultipleAnalysis={(analysis?.length || 0) > 1}
                      isActive={isActive}
                    />
                  );
                })}
              </View>
            );
          }}
        </VerticalList>
      </View>
      <View>
        <KeyPointsModal visible={showInfo} onClose={handleModalClose} />
      </View>
    </Fragment>
  );
}

function KeyPointsList({
  data,
  fixtures,
  hasPagination,
  hasMultipleAnalysis,
  isActive,
}: ListItemProp<SlipFixturesAnalysis>) {
  const fixtureInfo = useMemo(
    () => fixtures.find((fixture) => fixture.id === data.fixture_with_bet_id),
    [data.fixture_with_bet_id, fixtures]
  );

  const opacity = useSharedValue(isActive ? 1 : 0.12);

  const marketTextSplitted = fixtureInfo?.bet_market_text.split(" - ");

  useEffect(() => {
    opacity.value = withTiming(isActive ? 1 : 0.12);
  }, [isActive]);

  const animatedStyles = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  return (
    <Animated.View
      style={[
        styles.item,
        {
          minWidth: hasPagination ? 300 : undefined,
        },
        animatedStyles,
      ]}
    >
      {hasMultipleAnalysis && (
        <View>
          <Text numberOfLines={1}>
            <Body highlight>{fixtureInfo?.fixture_home_team_name} </Body>
            <Body color="secondary">vs </Body>
            <Body highlight>{fixtureInfo?.fixture_away_team_name}</Body>
          </Text>
          <Text numberOfLines={1}>
            <Body color="secondary">{marketTextSplitted?.[0]}: </Body>
            <Body>{marketTextSplitted?.[1]} </Body>
            <Body color="primary" highlight>
              {Number(fixtureInfo?.bet_odd).toFixed(2)}
            </Body>
          </Text>
        </View>
      )}
      {data.key_analysis.map((info, id) => (
        <KeyPointItem
          key={`key_points_point_${fixtureInfo?.fixture_home_team_name}_${fixtureInfo?.fixture_away_team_name}_${id}`}
          info={info}
        />
      ))}
    </Animated.View>
  );
}

function KeyPointItem({ info }: ItemProp) {
  return (
    <View style={styles.keyPoint}>
      <ArrowLeftIcon style={styles.keyPointIcon} color="#33C2EF" />
      <Body style={styles.keyPointText}>{info}</Body>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#121221",
    padding: 16,
    gap: 16,
    marginTop: 8,
  },
  heading: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    gap: 8,
  },
  keyPoints: {
    flexDirection: "row",
    gap: 16,
  },
  item: {
    flex: 1,
    gap: 16,
  },
  keyPoint: {
    flexDirection: "row",
    gap: 8,
    maxWidth: "100%",
  },
  keyPointIcon: {
    transform: [{ rotate: "180deg" }],
  },
  keyPointText: {
    flex: 1,
    paddingVertical: 2,
  },
});
