import { useState } from "react";
import { Pressable, StyleSheet, View } from "react-native";

import Heading from "@/components/typography/Heading";

import RateStarIcon from "@/components/icons/RateStar";

export default function AnalysisRate() {
  const [rating, setRating] = useState<number | undefined>();

  const handleRate = (newRate: number) => {
    if (rating !== undefined) return;

    setRating(newRate);
  };

  return (
    <View style={styles.container}>
      <Heading variant="200">Avaliar análise</Heading>
      <View style={styles.stars}>
        {Array.from({ length: 5 }).map((_, id) => (
          <Pressable key={`rate_star_${id}`} onPress={() => handleRate(id)}>
            <RateStarIcon isActive={!!rating && id <= rating} />
          </Pressable>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#121221",
    paddingVertical: 24,
    paddingHorizontal: 16,
    gap: 12,
    marginVertical: 8,
  },
  stars: {
    flexDirection: "row",
  },
});
