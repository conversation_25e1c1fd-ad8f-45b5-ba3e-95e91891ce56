import { Fragment, useMemo, useState } from "react";
import {
  NativeSyntheticEvent,
  StyleSheet,
  Text,
  TextLayoutEventData,
  View,
} from "react-native";
import { Route } from "@react-navigation/native";

import Tabs from "@/components/ui/tabs/Tabs";
import Navbar from "@/components/ui/navbar/Navbar";
import SlipPreviewModal from "@/components/ui/modals/SlipPreviewModal";

import Captions from "@/components/typography/Captions";

import { useSlipInfoContext } from "@/contexts/SlipInfoContext";

import { RouteParams } from "@/types/Routing";
import { SlipBetsInfo } from "@/types/SlipInfoContext";

interface AnalysisHeaderProps {
  scrollPosition: number;
  route?: Route<string, RouteParams>;
}

interface DescriptionProps {
  fixtures: Array<SlipBetsInfo> | undefined;
}

export default function AnalysisHeader({
  scrollPosition = 0,
  route,
}: AnalysisHeaderProps) {
  const [showInfo, setShowInfo] = useState(false);

  const { data } = useSlipInfoContext();

  const tabParam = useMemo(() => route?.params?.tab, []);
  const multipleAnalysis = false;

  const handleModalOpen = () => {
    setShowInfo(true);
  };

  const handleModalClose = () => {
    setShowInfo(false);
  };

  return (
    <Fragment>
      <Navbar
        label="Boletim #********"
        onLabelClick={handleModalOpen}
        description={<Description fixtures={data?.fixtures_with_bets} />}
        transparent={!multipleAnalysis && !!data}
        dismiss
        scrollPosition={scrollPosition}
        loading={!data}
      />
      {(multipleAnalysis || (!data && tabParam)) && (
        <Tabs defaultTab={tabParam} loading={!data} />
      )}
      <View>
        <SlipPreviewModal
          matches={data?.fixtures_with_bets}
          total_odds={data?.total_odds}
          image={data?.image}
          visible={showInfo}
          onClose={handleModalClose}
        />
      </View>
    </Fragment>
  );
}

function Description({ fixtures }: DescriptionProps) {
  const [moreTeams, setMoreTeams] = useState(0);

  const handleTextLayout = ({
    nativeEvent,
  }: NativeSyntheticEvent<TextLayoutEventData>) => {
    const maxSeparators = (fixtures?.length || 0) - 1;
    const displayedSeparators = (nativeEvent.lines?.[0].text.match(/•/g) || [])
      .length;
    const hiddenTeams = maxSeparators - displayedSeparators;

    if (hiddenTeams <= 0) return;

    setMoreTeams(hiddenTeams);
  };

  return (
    <View style={styles.container}>
      <Captions
        style={styles.description}
        numberOfLines={1}
        onTextLayout={handleTextLayout}
        align="center"
      >
        {fixtures?.map(
          ({ fixture_home_team_name, fixture_away_team_name }, id) => (
            <Text key={`analysis_header_team_${id}`}>
              {id > 0 && <Fragment> • </Fragment>}
              {fixture_home_team_name} <Text style={styles.vsText}>vs</Text>{" "}
              {fixture_away_team_name}
            </Text>
          )
        )}
      </Captions>
      {moreTeams > 0 && <Captions color="secondary">+{moreTeams}</Captions>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    display: "flex",
    flexDirection: "row",
    gap: 4,
    opacity: 0.6,
  },
  description: {
    flex: 1,
    overflow: "hidden",
    display: "flex",
  },
  vsText: {
    color: "#757588",
  },
});
