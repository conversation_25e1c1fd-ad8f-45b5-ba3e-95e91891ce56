import { useMemo } from "react";
import { Image, Platform, StyleSheet, Text, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

import { LinearGradient } from "expo-linear-gradient";

import Body from "@/components/typography/Body";
import Score from "@/components/ui/progress/Score";
import Heading from "@/components/typography/Heading";
import Captions from "@/components/typography/Captions";

import { getExpectedValueInfo, getScoreInfo } from "@/constants/analysis";

import ScoreSkeletonComponent from "./Skeletons/ScoreSkeleton";

const AnalysisBG = require("@/assets/images/analysisBG.png");

interface AnalysisScoreProps {
  score: number | null | undefined;
  trueProbability: string | null | undefined;
  inferredProbability: string | null | undefined;
  expectedValue: string | null | undefined;
  multipleAnalysis: boolean;
  loading?: boolean;
}

export default function AnalysisScore({
  score,
  trueProbability,
  inferredProbability,
  expectedValue,
  multipleAnalysis,
  loading,
}: AnalysisScoreProps) {
  const insets = useSafeAreaInsets();

  const scoreInfo = useMemo(() => {
    return getScoreInfo(score);
  }, [score]);

  const headerHeight = 48;
  const isWeb = Platform.OS === "web";

  const { ProbabilityIcon, ExpectedValueIcon, ...expectedValueInfo } =
    useMemo(() => {
      return getExpectedValueInfo(expectedValue);
    }, [expectedValue]);

  const formatProbabilityNumber = (probability: string | null | undefined) => {
    if (!probability) return "";

    const probabilityNumber = Number(probability) * 100;
    const formatDecimals = probabilityNumber.toFixed(1);
    const formatPercentage = String(formatDecimals).replace("%", "");

    return `${formatPercentage}%`;
  };

  if (loading)
    return <ScoreSkeletonComponent multipleAnalysis={multipleAnalysis} />;

  return (
    <View
      style={[
        styles.scoreContainer,
        {
          backgroundColor: scoreInfo.backgroundColor,
        },
      ]}
    >
      <Image source={AnalysisBG} style={[styles.scoreBg]} />
      <LinearGradient
        colors={["#121221", "transparent"]}
        locations={[0, 0.65]}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        style={styles.scoreBg}
      />
      <View
        style={[
          styles.scoreContent,
          {
            paddingTop:
              insets.top + headerHeight + (multipleAnalysis ? 40 + 40 : 24), // 40 = tabs height && 40||24 = margin top
          },
        ]}
      >
        <View style={styles.progressContainer}>
          <Score
            score={score || 0}
            color={scoreInfo.color}
            indicatorColor={scoreInfo.indicatorColor}
          />
          <View style={styles.progressDescription}>
            <Heading style={{ color: scoreInfo.color }}>
              <Text style={styles.currentScore}>{score}</Text>/10
            </Heading>
            <Heading
              style={{
                color: scoreInfo.color,
                fontSize: isWeb ? 10 : undefined,
                lineHeight: isWeb ? 14 : undefined,
              }}
              align="center"
            >
              GRAU DE CONFIANÇA
            </Heading>
          </View>
        </View>
        <Body>{scoreInfo.message}</Body>
        {expectedValue && (
          <View style={styles.infoCards}>
            <View style={styles.infoCard}>
              {ProbabilityIcon && (
                <ProbabilityIcon color={expectedValueInfo.color} />
              )}
              <View>
                <Captions variant="200">PROBABILIDADE</Captions>
                <Heading
                  variant="200"
                  style={{ color: expectedValueInfo.color }}
                >
                  {formatProbabilityNumber(trueProbability)} vs{" "}
                  {formatProbabilityNumber(inferredProbability)}
                </Heading>
              </View>
              <Captions variant="200">
                Possibilidade estatística vs. probabilidade implícita da casa de
                apostas
              </Captions>
            </View>
            <View style={styles.infoCard}>
              {ExpectedValueIcon && (
                <ExpectedValueIcon color={expectedValueInfo.color} />
              )}
              <View>
                <Captions variant="200">VALOR ESPERADO</Captions>
                <Heading
                  variant="200"
                  style={{ color: expectedValueInfo.color }}
                >
                  {expectedValueInfo.evaluation}
                </Heading>
              </View>
              <Captions variant="200">{expectedValueInfo.reply}</Captions>
            </View>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  scoreContainer: {
    position: "relative",
  },
  scoreContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    gap: 16,
  },
  progressContainer: {
    height: 100,
    alignItems: "center",
  },
  scoreBg: {
    position: "absolute",
    width: "100%",
    height: "100%",
    top: 0,
    left: 0,
  },
  progressDescription: {
    position: "absolute",
    alignItems: "center",
    justifyContent: "center",
    top: 16,
    maxWidth: 100,
  },
  currentScore: {
    fontSize: 34,
    lineHeight: 40,
  },
  infoCards: {
    display: "flex",
    flexDirection: "row",
    gap: 12,
  },
  infoCard: {
    width: "100%",
    backgroundColor: "#0909107A",
    flexShrink: 1,
    borderRadius: 8,
    padding: 16,
    gap: 8,
  },
});
