import { StyleSheet, View } from "react-native";

import SkeletonIcon from "@/components/icons/Skeleton";

export default function KeyPointsSkeleton() {
  return (
    <View style={styles.container}>
      <View style={styles.heading}>
        <SkeletonIcon width={235} height={16} />
        <SkeletonIcon width={24} height={24} />
      </View>
      {Array.from({ length: 5 }).map((_, id) => (
        <View key={`key_points_skeleton_item_${id}`} style={styles.item}>
          <SkeletonIcon width={24} height={24} />
          <View style={styles.keyPoint}>
            <SkeletonIcon width={276} height={10} />
            <SkeletonIcon width={192} height={10} />
            <SkeletonIcon width={235} height={10} />
          </View>
        </View>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#121221",
    padding: 16,
    gap: 16,
    marginTop: 8,
  },
  heading: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  item: {
    flexDirection: "row",
    paddingVertical: 8,
    gap: 8,
  },
  keyPoint: {
    gap: 10,
  },
});
