import { StyleSheet, View } from "react-native";

import SkeletonIcon from "@/components/icons/Skeleton";

export default function H2HSkeleton() {
  return (
    <View style={styles.container}>
      <View style={styles.heading}>
        <SkeletonIcon width={235} height={16} />
      </View>
      <View style={styles.H2HItem}>
        {Array.from({ length: 5 }).map((_, id) => (
          <View key={`H2H_skeleton_match_${id}`} style={styles.matchCard}>
            <View style={styles.teamsName}>
              <SkeletonIcon width={60} height={10} />
              <SkeletonIcon width={80} height={10} />
            </View>
            <SkeletonIcon width={24} height={36} radius={4} />
            <View style={styles.separator} />
            <SkeletonIcon width={60} height={10} />
          </View>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#121221",
    padding: 16,
    gap: 16,
    marginTop: 8,
  },
  heading: {
    height: 22,
    justifyContent: "center",
  },
  H2HItem: {
    overflow: "hidden",
    borderRadius: 8,
    gap: 2,
  },
  matchCard: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#171728",
    paddingVertical: 5,
    paddingHorizontal: 12,
    gap: 8,
  },
  teamsName: {
    flexGrow: 1,
    marginVertical: 4,
    gap: 10,
  },
  awayTeamName: {
    marginTop: -2,
  },
  homeTeamGoals: {
    marginTop: 1,
  },
  awayTeamGoals: {
    marginTop: -4,
  },
  separator: {
    width: 1,
    height: "100%",
    backgroundColor: "#1E1E30",
  },
});
