import { StyleSheet, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

import SkeletonIcon from "@/components/icons/Skeleton";
import ScoreSkeleton from "@/components/ui/progress/ScoreSkeleton";

interface ScoreSkeletonProps {
  multipleAnalysis: boolean;
}

export default function ScoreSkeletonComponent({
  multipleAnalysis,
}: ScoreSkeletonProps) {
  const insets = useSafeAreaInsets();

  const headerHeight = 48;

  return (
    <View
      style={[
        styles.content,
        {
          paddingTop:
            insets.top + headerHeight + (multipleAnalysis ? 40 + 40 : 24), // 40 = ta
        },
      ]}
    >
      <View style={styles.progress}>
        <ScoreSkeleton />
        <View style={styles.progressDescription}>
          <SkeletonIcon width={24} height={24} style={{ marginBottom: 12 }} />
          <SkeletonIcon width={58} height={10} style={{ marginBottom: 10 }} />
          <SkeletonIcon width={32} height={10} />
        </View>
      </View>
      <View style={styles.message}>
        <SkeletonIcon width={276} height={10} />
        <SkeletonIcon width={192} height={10} />
        <SkeletonIcon width={235} height={10} />
      </View>
      <View style={styles.infoCards}>
        {Array.from({ length: 2 }).map((_, id) => (
          <View key={`score_skeleton_info_card_${id}`} style={styles.infoCard}>
            <SkeletonIcon width={24} height={24} />
            <View style={{ gap: 10 }}>
              <SkeletonIcon width={105} height={10} />
              <SkeletonIcon width={127} height={10} />
            </View>
            <View style={{ paddingTop: 12, gap: 10 }}>
              <SkeletonIcon width={105} height={10} />
              <SkeletonIcon width={127} height={10} />
              <SkeletonIcon width={77} height={10} />
            </View>
          </View>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  content: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    gap: 16,
    backgroundColor: "#121221",
  },
  progress: {
    position: "relative",
    alignItems: "center",
  },
  progressDescription: {
    position: "absolute",
    alignItems: "center",
    justifyContent: "center",
    top: 21,
  },
  message: {
    gap: 10,
  },
  infoCards: {
    display: "flex",
    flexDirection: "row",
    gap: 12,
  },
  infoCard: {
    backgroundColor: "#0909107A",
    flex: 1,
    padding: 16,
    gap: 8,
    borderRadius: 8,
  },
});
