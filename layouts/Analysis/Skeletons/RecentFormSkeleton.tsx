import { StyleSheet, View } from "react-native";

import SkeletonIcon from "@/components/icons/Skeleton";

export default function RecentFormSkeleton() {
  return (
    <View style={styles.container}>
      <View style={styles.heading}>
        <SkeletonIcon width={235} height={16} />
      </View>
      <View style={styles.recentFormList}>
        {Array.from({ length: 2 }).map((_, id) => (
          <View key={`recent_form_skeleton_item_${id}`} style={styles.item}>
            <SkeletonIcon
              width={80}
              height={10}
              style={{ marginVertical: 5 }}
            />
            <View style={styles.formList}>
              <SkeletonIcon width={20} height={20} />
              <SkeletonIcon width={20} height={20} />
              <SkeletonIcon width={20} height={20} />
              <SkeletonIcon width={20} height={20} />
              <SkeletonIcon width={20} height={20} />
              <SkeletonIcon width={20} height={20} />
            </View>
          </View>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#121221",
    padding: 16,
    gap: 16,
    marginTop: 8,
  },
  heading: {
    height: 22,
    justifyContent: "center",
  },
  recentFormList: {
    flexDirection: "row",
    overflow: "hidden",
    gap: 1,
    borderRadius: 8,
  },
  item: {
    alignItems: "center",
    backgroundColor: "#171728",
    flexGrow: 1,
    paddingVertical: 10,
    gap: 8,
  },
  formList: {
    flexDirection: "row",
    gap: 2,
  },
});
