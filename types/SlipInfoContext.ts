import { Dispatch, SetStateAction } from "react";

import { ImagePickerAsset } from "expo-image-picker";
import { DocumentPickerAsset } from "expo-document-picker";

type DocumentAsset = DocumentPickerAsset &
  Pick<ImagePickerAsset, "width" | "height">;

export type ImageState = ImagePickerAsset | DocumentAsset | undefined;

export interface SlipBetsInfo {
  id: string;
  approved_by_user: boolean | null;
  fixture_home_team_name: string;
  fixture_away_team_name: string;
  fixture_datetime: string | null;
  bet_market_text: string;
  bet_odd: string | null;
  bet_stake: string | null;
  bet_potential_return: string | null;
}

export interface SlipH2HFixturesInfo {
  home_goals: number | null;
  away_goals: number | null;
  home_team: string | null;
  away_team: string | null;
  date: string;
}

export interface SlipFixturesAnalysis {
  key_analysis: Array<string>;
  relevant_streaks: Array<string>;
  home_team_form: Array<number>;
  away_team_form: Array<number>;
  historic_h2h_fixtures: Array<SlipH2HFixturesInfo>;
  fixture_with_bet_id: string | null;
}

interface SlipAnalysisInfo {
  true_probability: string | null;
  inferred_probability: string | null;
  expected_value: string | null;
  confidence_level: number | null;
  fixtures_analysis: Array<SlipFixturesAnalysis>;
}

export interface SlipInfoData {
  id: string;
  processing_status: number;
  slip_type: number | null;
  total_odds: string | null;
  total_stake: string | null;
  total_potential_return: string | null;
  currency: string | null;
  fixtures_with_bets: Array<SlipBetsInfo>;
  analysis: SlipAnalysisInfo;
  image: ImagePickerAsset | DocumentAsset | undefined;
}

export interface SlipInfoContextType {
  data: SlipInfoData | undefined;
  setData: Dispatch<SetStateAction<SlipInfoData | undefined>>;
}
