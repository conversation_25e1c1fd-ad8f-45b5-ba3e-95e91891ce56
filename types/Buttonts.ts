import { ReactNode } from "react";
import { StyleProp, ViewStyle } from "react-native";

export interface BasicButtonProps {
  children: React.ReactNode;
  onPress: () => void;
  style?: StyleProp<ViewStyle>;
}

export interface ButtonProps extends BasicButtonProps {
  variant?: "primary" | "secondary";
  icon?: ReactNode;
  withIcon?: boolean;
  loading?: boolean;
}

export interface ButtonIconProps
  extends Pick<ButtonProps, "icon" | "loading"> {}
