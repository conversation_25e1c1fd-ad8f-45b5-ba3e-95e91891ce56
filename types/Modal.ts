import { ReactNode } from "react";
import { View } from "react-native";

import { UploadStatus } from "./Common";

export interface ModalProps {
  visible: boolean;
  onClose: () => void;
  closeOutside?: boolean;
  moveable?: boolean;
  children: ReactNode;
}

export interface ModalUploadProps {
  onUploadChange: (status: UploadStatus, error?: number | boolean) => void;
}

export interface ModalProcessingProps extends ModalUploadProps {
  error: number | boolean;
}

export interface ModalContentProps {
  error?: number | boolean;
  onChange: (status: UploadStatus, error?: number | boolean) => void;
}

export interface ModalViewRef extends View {
  __nativeTag: string;
}
