import { ReactNode } from "react";
import { StyleProp, TextProps, TextStyle } from "react-native";

export interface TypographyProps extends TextProps {
  children: ReactNode;
  style?: StyleProp<TextStyle>;
  variant?: "50" | "100" | "200" | "300" | "400";
  color?: "primary" | "secondary" | "white" | "inverted" | "disabled";
  align?: "left" | "center" | "right";
  highlight?: boolean;
  onPress?: () => void;
}
