import { JSX<PERSON>lementConstructor, ReactElement, ReactNode } from "react";
import { NativeGesture } from "react-native-gesture-handler";

import { IconProps } from "./Icon";
import {
  SlipBetsInfo,
  SlipFixturesAnalysis,
  SlipH2HFixturesInfo,
} from "./SlipInfoContext";

export interface ComponentProps {
  children: ReactNode;
}

export type UploadStatus = "upload" | "analysis" | "error";

export interface MatchProps {
  home_team: string;
  away_team: string;
  winner: string;
  odd: number;
}
export interface MatchesListProps {
  matches: Array<SlipBetsInfo> | undefined;
  total_odds: string | null | undefined;
}

export interface MatchesListItemProps extends SlipBetsInfo {
  isCombos: boolean | undefined;
  index: number;
  total_odds: string | null | undefined;
}

export interface FunctionProp {
  [key: string]: () => void;
}

export interface ProcessingItem {
  id: number;
  text: string;
  description: string;
  Icon: (props: IconProps) => JSX.Element;
}

export interface ProcessingItemProps extends ProcessingItem {
  activeStep: number;
  progress: number;
  processLength: number;
}

export interface AnalysisComponentProps {
  scrollGesture: NativeGesture;
  analysis: Array<SlipFixturesAnalysis> | undefined;
  fixtures: Array<SlipBetsInfo>;
  loading?: boolean;
}

export interface AnalysisInfo {
  home_team: string;
  away_team: string;
  info: Array<string>;
}

export interface AnalysisRecentFormInfo {
  home_team: string;
  home_team_form: Array<FormType>;
  away_team: string;
  away_team_form: Array<FormType>;
}

export type H2HMatches = {
  winner: "home" | "away" | null;
  home_goals: number;
  away_goals: number;
  date: string;
};

export interface AnalysisH2HInfo {
  home_team: string;
  away_team: string;
  matches: Array<H2HMatches>;
}

export interface ListItemProp<T> {
  data: T;
  fixtures: Array<SlipBetsInfo>;
  hasPagination: boolean;
  hasMultipleAnalysis: boolean;
  isActive: boolean;
}

export interface ItemProp {
  info: string;
}

export interface RecentFormItemProp {
  homeTeam: string;
  homeTeamForm: Array<number>;
  awayTeam: string;
  awayTeamForm: Array<number>;
}

export interface H2HItemProp {
  matches: Array<SlipH2HFixturesInfo>;
}

export interface VerticalListProps {
  dataLength: number;
  type: string;
  children: (
    props: VerticalListReturnProps
  ) => ReactElement<any, string | JSXElementConstructor<any>>;
  scrollGesture: NativeGesture;
}

export interface VerticalListReturnProps {
  hasPagination: boolean;
  activeIndex: number;
}

export interface BulletPointProps {
  activeIndex: number;
  index: number;
}

export type FormType = number | "upcoming";

export interface FormProps {
  type: FormType;
}
