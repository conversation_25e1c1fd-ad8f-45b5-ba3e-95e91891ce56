import { createContext, useContext, useState } from "react";

import { ComponentProps } from "@/types/Common";
import { SlipInfoContextType, SlipInfoData } from "@/types/SlipInfoContext";

const SlipInfoContext = createContext<SlipInfoContextType>({
  data: undefined,
  setData: () => null,
});

export function SlipInfoProvider({ children }: ComponentProps) {
  const [data, setData] = useState<SlipInfoData | undefined>(undefined);

  return (
    <SlipInfoContext.Provider value={{ data, setData }}>
      {children}
    </SlipInfoContext.Provider>
  );
}

export function useSlipInfoContext() {
  return useContext(SlipInfoContext);
}
