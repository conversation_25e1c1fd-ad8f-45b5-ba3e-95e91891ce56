import { FetchProps, FetchSetting } from "@/types/Api";

export async function handleFetch({
  path,
  method = "GET",
  signal,
  body,
  isMultipart = false,
}: FetchProps) {
  try {
    const settings: FetchSetting = {
      method,
      signal,
      body,
      headers: {
        Accept: "application/json",
      },
    };

    if (!isMultipart && method === "POST") {
      settings.headers["Content-Type"] = "application/json";
    }

    const res = await fetch(
      `${process.env.EXPO_PUBLIC_API_URL}${path}`,
      settings
    );

    if (!res.ok)
      return {
        ok: false,
      };

    const resJson = await res.json();

    return { ok: true, ...resJson };
  } catch (err) {
    return {
      ok: false,
    };
  }
}
