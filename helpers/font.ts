import { Platform } from "react-native";

export const getFontFamily = (fontName: string) => {
  if (Platform.OS !== "web" || !fontName.includes("BaiJamjuree"))
    return fontName;

  return "Bai Jamjuree";
};

export const getFontWeight = (fontName: string): any => {
  if (Platform.OS !== "web" || !fontName.includes("BaiJamjuree"))
    return "normal";

  const fontWeight = fontName.split("_")[1];

  return fontWeight.slice(0, 3);
};
