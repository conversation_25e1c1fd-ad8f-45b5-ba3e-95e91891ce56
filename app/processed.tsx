import { Fragment, useEffect, useState } from "react";
import { Image, Platform, ScrollView, StyleSheet, View } from "react-native";

import { Redirect, useRouter } from "expo-router";
import { useShareIntentContext } from "expo-share-intent";

import Analyzing from "@/layouts/Processed/Analyzing";

import Body from "@/components/typography/Body";
import Heading from "@/components/typography/Heading";

import Button from "@/components/ui/buttons/Button";
import MatchesList from "@/components/ui/common/MatchesList";
import BottomModal from "@/components/ui/modals/BottomModal";
import InlineNotification from "@/components/ui/notifications/InlineNotification";

import CpuIcon from "@/components/icons/Cpu";

import { useSlipInfoContext } from "@/contexts/SlipInfoContext";

import { handleFetch } from "@/helpers/api";

interface ApproveFixturesBody {
  fixture_ids: Record<string, boolean>;
}

export default function Processed() {
  const [analyze, setAnalyze] = useState<boolean | "backed">(false);
  const [loading, setLoading] = useState(false);

  const router = useRouter();

  const { data, setData } = useSlipInfoContext();
  const { hasShareIntent } = useShareIntentContext();

  useEffect(() => {
    if (!hasShareIntent) return;

    const canDismiss = router.canDismiss();

    canDismiss ? router.dismissAll() : router.replace("/");
  }, [hasShareIntent]);

  if (Platform.OS === "web") {
    return <Redirect href={"/not-found" as any} />;
  }

  const handleContinue = async () => {
    // Needed because the slip doesn't accept confirmation twice
    // So added this back logic for now
    if (analyze === "backed") {
      setAnalyze(true);
      return;
    }

    const fixturesIds = data?.fixtures_with_bets?.map(({ id }) => id);
    const body: ApproveFixturesBody = {
      fixture_ids: {},
    };

    fixturesIds?.forEach((id) => {
      body.fixture_ids[id] = true;
    });

    setLoading(true);

    const apiRes = await handleFetch({
      path: `slips/${data?.id}/actions/set-approved-fixture-statuses/`,
      method: "POST",
      body: JSON.stringify(body),
    });

    setLoading(false);

    if (!apiRes.ok || apiRes.processing_status !== 3) {
      // Some error;
      return;
    }

    setData((prev) => ({ ...prev, ...apiRes }));

    setAnalyze(true);
  };

  const handleClose = () => {
    setAnalyze("backed");
  };

  return (
    <Fragment>
      <View style={styles.container}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
        >
          <View style={styles.content}>
            <Heading variant="200">Jogos identificados</Heading>
            <InlineNotification description="Confira se todos os jogos e seleções foram identificados corretamente antes de continuar" />
            <MatchesList
              matches={data?.fixtures_with_bets}
              total_odds={data?.total_odds}
            />
            {data?.image && (
              <Image
                source={{ uri: data?.image.uri }}
                style={styles.image}
                resizeMode="contain"
              />
            )}
          </View>
        </ScrollView>
        <View style={styles.stickyButton}>
          <Button
            icon={<CpuIcon color="#FFFFFF" />}
            onPress={handleContinue}
            loading={loading}
          >
            <Body highlight color="white">
              Continuar com a análise
            </Body>
          </Button>
        </View>
      </View>
      <BottomModal visible={analyze === true} onClose={handleClose}>
        <Analyzing onBack={handleClose} />
      </BottomModal>
    </Fragment>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 8,
    backgroundColor: "#0F0F1B",
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  content: {
    display: "flex",
    backgroundColor: "#121221",
    padding: 16,
    gap: 16,
    flex: 1,
    flexGrow: 1,
    marginBottom: 8,
  },
  image: {
    aspectRatio: 1,
    borderRadius: 8,
  },
  stickyButton: {
    backgroundColor: "#121221",
    borderTopColor: "#27273C",
    padding: 16,
    borderTopWidth: 1,
  },
});
