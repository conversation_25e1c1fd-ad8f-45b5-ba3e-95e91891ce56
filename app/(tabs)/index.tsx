import { useEffect, useState } from "react";
import { useNavigation } from "@react-navigation/native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { View, StyleSheet, Pressable, Platform } from "react-native";

import { Image } from "expo-image";
import { Redirect } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useShareIntentContext } from "expo-share-intent";

import UploadModal from "@/layouts/UploadModals/UploadModal";
import ProcessingModal from "@/layouts/UploadModals/ProcessingModal";

import Body from "@/components/typography/Body";
import Heading from "@/components/typography/Heading";
import Captions from "@/components/typography/Captions";

import BottomModal from "@/components/ui/modals/BottomModal";

import PlusIcon from "@/components/icons/Plus";
import UsersIcon from "@/components/icons/Users";

import { UploadStatus } from "@/types/Common";

const Logo = require("@/assets/images/logo.png");

export default function Index() {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();

  const [{ isModalVisible, uploadStatus, error }, setData] = useState<{
    isModalVisible: boolean;
    uploadStatus: UploadStatus;
    error: number | boolean;
  }>({ isModalVisible: false, uploadStatus: "upload", error: false });

  const { hasShareIntent } = useShareIntentContext();

  useEffect(() => {
    const unsubscribe = navigation.addListener("blur", () => {
      setData({ isModalVisible: false, uploadStatus: "upload", error: false });
    });

    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    if (!hasShareIntent) return;

    setData({ isModalVisible: true, uploadStatus: "upload", error: false });
  }, [hasShareIntent]);

  if (Platform.OS === "web") {
    return <Redirect href={"/not-found" as any} />;
  }

  const handleModalOpen = () => {
    setData((prev) => ({ ...prev, isModalVisible: true }));
  };

  const handleModalClose = () => {
    setData({ isModalVisible: false, uploadStatus: "upload", error: false });
  };

  const handleUploadChange = (
    status: UploadStatus,
    error?: number | boolean
  ) => {
    setData((prev) => ({
      ...prev,
      uploadStatus: status,
      error: error || false,
    }));
  };

  const getUploadModal = () => {
    switch (uploadStatus) {
      case "upload":
        return <UploadModal onUploadChange={handleUploadChange} />;
      case "analysis":
        return (
          <ProcessingModal error={error} onUploadChange={handleUploadChange} />
        );
      case "error":
        return (
          <ProcessingModal error={error} onUploadChange={handleUploadChange} />
        );
    }
  };

  return (
    <View style={{ ...styles.container, paddingTop: 23 + insets.top }}>
      <View style={styles.header}>
        <Image source={Logo} style={styles.image} />
        <Heading variant="300">Analisar boletim de apostas</Heading>
        <Body variant="200" color="secondary">
          Análise de seus boletins de apostas com base em IA para fornecer
          informações valiosas em segundos
        </Body>
      </View>
      <Pressable style={styles.upload} onPress={handleModalOpen}>
        <PlusIcon size={40} color="#C5C5D3" />
        <Heading variant="200">Carregue o seu boletim de apostas</Heading>
        <Captions variant="200" color="secondary">
          Pode fazer upload de imagens JPEG, PNG e GIF de até 5 MB
        </Captions>
      </Pressable>
      <View style={styles.trustUs}>
        <Heading color="secondary">
          Por que os apostadores confiam em nós?
        </Heading>
        <View style={styles.trustUsDescription}>
          <UsersIcon color="#757588" />
          <Captions variant="200" color="secondary">
            Confiado por mais de 50.000 apostadores para melhorar sua estratégia
          </Captions>
        </View>
      </View>
      <StatusBar style="light" />
      <BottomModal
        visible={isModalVisible}
        onClose={handleModalClose}
        closeOutside={uploadStatus !== "analysis"}
      >
        {getUploadModal()}
      </BottomModal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    display: "flex",
    height: "100%",
    backgroundColor: "#121221",
    paddingRight: 16,
    paddingBottom: 40,
    paddingLeft: 16,
    gap: 40,
  },
  header: {
    display: "flex",
    gap: 8,
  },
  image: {
    width: 57,
    height: 32,
  },
  upload: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    borderColor: "#27273C",
    borderStyle: "dashed",
    gap: 8,
    borderWidth: 2,
    borderRadius: 16,
    flexGrow: 1,
  },
  trustUs: {
    display: "flex",
    gap: 8,
  },
  trustUsDescription: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
});
