import { Tabs } from "expo-router";

import CpuIcon from "@/components/icons/Cpu";
import HistoryIcon from "@/components/icons/History";
import MagnifierIcon from "@/components/icons/Magnifier";

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        tabBarLabelStyle: {
          fontFamily: "BaiJamjuree_500Medium",
          lineHeight: 12,
        },
        tabBarActiveTintColor: "#FD4C21",
        tabBarStyle: {
          backgroundColor: "#121221",
          borderTopWidth: 0,
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: "Analisador",
          headerShown: false,
          tabBarIcon: ({ color }) => <CpuIcon color={color} />,
        }}
      />
      <Tabs.Screen
        name="search"
        options={{
          title: "Busca",
          tabBarLabelStyle: { color: "#454559" },
          tabBarIcon: () => <MagnifierIcon color="#454559" />,
          tabBarItemStyle: { pointerEvents: "none" },
        }}
      />
      <Tabs.Screen
        name="history"
        options={{
          title: "Histórico",
          tabBarLabelStyle: { color: "#454559" },
          tabBarIcon: () => <HistoryIcon color="#454559" />,
          tabBarItemStyle: { pointerEvents: "none" },
        }}
      />
    </Tabs>
  );
}
