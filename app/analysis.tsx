import { Fragment, useEffect, useRef } from "react";
import {
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  ScrollView,
  Share,
  StatusBar,
  StyleSheet,
  View,
} from "react-native";
import {
  Gesture,
  GestureDetector,
  GestureHandlerRootView,
} from "react-native-gesture-handler";

import Head from "expo-router/head";
import * as Linking from "expo-linking";
import * as Application from "expo-application";
import { useShareIntentContext } from "expo-share-intent";
import { useLocalSearchParams, useNavigation, useRouter } from "expo-router";

import Body from "@/components/typography/Body";

import GetApp from "@/components/ui/common/GetApp";
import Button from "@/components/ui/buttons/Button";

import ShareIcon from "@/components/icons/Share";

import AnalysisH2H from "@/layouts/Analysis/H2H";
import AnalysisRate from "@/layouts/Analysis/Rate";
import AnalysisScore from "@/layouts/Analysis/Score";
import AnalysisHeader from "@/layouts/Analysis/Header";
import AnalysisStreaks from "@/layouts/Analysis/Streaks";
import AnalysisKeyPoints from "@/layouts/Analysis/KeyPoints";
import AnalysisRecentForm from "@/layouts/Analysis/RecentForm";

import { useSlipInfoContext } from "@/contexts/SlipInfoContext";

import { handleFetch } from "@/helpers/api";

export default function Analysis() {
  const prevScrollValueRef = useRef(0);

  const router = useRouter();
  const url = Linking.useURL();
  const native = Gesture.Native();
  const navigation = useNavigation();
  const localSearchParams = useLocalSearchParams();

  const { data, setData } = useSlipInfoContext();
  const { hasShareIntent } = useShareIntentContext();

  const isWeb = Platform.OS === "web";
  const multipleAnalysis = false;

  const getSharedSlip = async (slipId: string) => {
    const res = await handleFetch({
      path: `slips/${slipId}`,
    });

    if (!res.ok) {
      // Some error
      return;
    }

    setData(res);
  };

  useEffect(() => {
    if (!hasShareIntent) return;

    const canDismiss = router.canDismiss();

    canDismiss ? router.dismissAll() : router.replace("/");
  }, [hasShareIntent]);

  useEffect(() => {
    let slipId;

    if (url) {
      const { queryParams } = Linking.parse(url);

      slipId = queryParams?.["id"];
    }

    if (localSearchParams) {
      const { id } = localSearchParams;

      slipId = id;
    }

    if (!slipId || Array.isArray(slipId)) return;

    getSharedSlip(slipId);
  }, []);

  const handleShare = async () => {
    const { tab } = localSearchParams;

    await Share.share({
      title: "Boletim #31032025",
      message: `${Application.applicationId}://analysis?id=${data?.id}${
        multipleAnalysis ? `&tab=${tab || 0}` : ""
      }`,
      url: `${Application.applicationId}://analysis=${data?.id}${
        multipleAnalysis ? `&tab=${tab || 0}` : ""
      }`,
    });
  };

  const handleScroll = ({
    nativeEvent,
  }: NativeSyntheticEvent<NativeScrollEvent>) => {
    const positionY = nativeEvent.contentOffset.y;

    if (positionY > 100 && prevScrollValueRef.current > 100) return;

    prevScrollValueRef.current = positionY;
    navigation.setOptions({
      header: () => AnalysisHeader({ scrollPosition: positionY }),
    });
  };

  return (
    <Fragment>
      <Head>
        <title>Valida Bets: IA para palpites e apostas</title>
        <meta
          name="description"
          content="Valide palpites, analise riscos e aposte melhor com inteligência artificial."
        />
      </Head>
      <View style={[styles.container, isWeb ? styles.webContainer : undefined]}>
        <GestureHandlerRootView>
          <GestureDetector gesture={native}>
            <ScrollView
              style={styles.scrollView}
              onScroll={handleScroll}
              scrollEventThrottle={16}
              showsHorizontalScrollIndicator={false}
            >
              <StatusBar translucent backgroundColor="transparent" />
              <AnalysisScore
                score={data?.analysis?.confidence_level}
                trueProbability={data?.analysis?.true_probability}
                inferredProbability={data?.analysis?.inferred_probability}
                expectedValue={data?.analysis?.expected_value}
                multipleAnalysis={
                  multipleAnalysis || !!(!data && localSearchParams.tab)
                }
                loading={!data}
              />
              <AnalysisKeyPoints
                scrollGesture={native}
                analysis={data?.analysis?.fixtures_analysis}
                fixtures={data?.fixtures_with_bets || []}
                loading={!data}
              />
              <AnalysisStreaks
                scrollGesture={native}
                analysis={data?.analysis?.fixtures_analysis}
                fixtures={data?.fixtures_with_bets || []}
                loading={!data}
              />
              <AnalysisRecentForm
                scrollGesture={native}
                analysis={data?.analysis?.fixtures_analysis}
                fixtures={data?.fixtures_with_bets || []}
                loading={!data}
              />
              <AnalysisH2H
                scrollGesture={native}
                analysis={data?.analysis?.fixtures_analysis}
                fixtures={data?.fixtures_with_bets || []}
                loading={!data}
              />
              {isWeb ? <GetApp /> : <AnalysisRate />}
            </ScrollView>
          </GestureDetector>
        </GestureHandlerRootView>
        {!isWeb && (
          <View style={styles.stickyButton}>
            <Button
              variant="secondary"
              icon={<ShareIcon />}
              onPress={handleShare}
            >
              <Body highlight align="center">
                Compartilhar análise
              </Body>
            </Button>
          </View>
        )}
      </View>
    </Fragment>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#0F0F1B",
    boxShadow: "0px 0px 20px 20px #000000",
    flex: 1,
  },
  webContainer: {
    position: "relative",
    alignSelf: "center",
    width: "100%",
    maxWidth: 375,
  },
  scrollView: {
    flex: 1,
  },
  stickyButton: {
    backgroundColor: "#121221",
    borderColor: "#27273C",
    padding: 16,
    borderTopWidth: 1,
  },
});
