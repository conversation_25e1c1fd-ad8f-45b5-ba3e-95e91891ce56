import { useEffect } from "react";
import { Platform } from "react-native";
import { Route } from "@react-navigation/native";

import { Stack } from "expo-router";
import * as NavigationBar from "expo-navigation-bar";
import { ShareIntentProvider } from "expo-share-intent";

import AnalysisHeader from "@/layouts/Analysis/Header";
import ProcessedHeader from "@/layouts/Processed/Header";

import { SlipInfoProvider } from "@/contexts/SlipInfoContext";

import { RouteParams } from "@/types/Routing";

export default function RootLayout() {
  NavigationBar.setBackgroundColorAsync("#121221");

  useEffect(() => {
    if (Platform.OS === "web") {
      const link = document.createElement("link");
      link.rel = "stylesheet";
      link.href =
        "https://fonts.googleapis.com/css2?family=Bai+Jamjuree:wght@200;300;400;500;600;700&display=swap";
      document.head.appendChild(link);
    }
  }, []);

  return (
    <ShareIntentProvider>
      <SlipInfoProvider>
        <Stack>
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen
            name="processed"
            options={{
              presentation: "modal",
              header: ProcessedHeader,
            }}
          />
          <Stack.Screen
            name="analysis"
            options={{
              header: ({ route }) =>
                AnalysisHeader({
                  scrollPosition: 0,
                  route: route as Route<string, RouteParams>,
                }),
              statusBarBackgroundColor: "transparent",
              headerTransparent: true,
              contentStyle: { backgroundColor: "#0F0F1B" },
            }}
          />
        </Stack>
      </SlipInfoProvider>
    </ShareIntentProvider>
  );
}
