const IS_DEV = process.env.APP_VARIANT === "development";

export default {
  expo: {
    name: IS_DEV
      ? "Valida Bets: IA para palpites e apostas (Dev)"
      : "Valida Bets: IA para palpites e apostas",
    slug: "validabets",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/images/icon.png",
    scheme: "validabets.ai",
    userInterfaceStyle: "dark",
    backgroundColor: "#0F0F1B",
    newArchEnabled: true,
    platforms: ["ios", "android", "web"],
    ios: {
      supportsTablet: true,
      bundleIdentifier: IS_DEV ? "com.validabets.ai.dev" : "com.validabets.ai",
    },
    android: {
      permissions: [
        "android.permission.CAMERA",
        "android.permission.READ_EXTERNAL_STORAGE",
      ],
      package: IS_DEV ? "com.validabets.ai.dev" : "com.validabets.ai",
    },
    web: {
      output: "static",
    },
    plugins: [
      "expo-router",
      "expo-document-picker",
      "expo-image-picker",
      [
        "expo-splash-screen",
        {
          image: "./assets/images/logo.png",
          imageWidth: 85.92,
          backgroundColor: "#121221",
        },
      ],
      [
        "expo-font",
        {
          fonts: [
            "node_modules/@expo-google-fonts/bai-jamjuree/200ExtraLight/BaiJamjuree_200ExtraLight.ttf",
            "node_modules/@expo-google-fonts/bai-jamjuree/300Light/BaiJamjuree_300Light.ttf",
            "node_modules/@expo-google-fonts/bai-jamjuree/400Regular/BaiJamjuree_400Regular.ttf",
            "node_modules/@expo-google-fonts/bai-jamjuree/500Medium/BaiJamjuree_500Medium.ttf",
            "node_modules/@expo-google-fonts/bai-jamjuree/600SemiBold/BaiJamjuree_600SemiBold.ttf",
            "node_modules/@expo-google-fonts/bai-jamjuree/700Bold/BaiJamjuree_700Bold.ttf",
          ],
        },
      ],
      [
        "expo-share-intent",
        {
          iosActivationRules: {
            NSExtensionActivationSupportsImageWithMaxCount: 1,
          },
          androidIntentFilters: ["image/*"],
        },
      ],
    ],
    experiments: {
      typedRoutes: true,
    },
    extra: {
      router: {
        origin: false,
      },
      eas: {
        projectId: "fb31f8f5-7280-4030-a433-231a82f33082",
      },
    },
  },
};
