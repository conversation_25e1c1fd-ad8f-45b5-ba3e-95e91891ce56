import { StyleProp, StyleSheet, View, ViewStyle } from "react-native";

import Captions from "@/components/typography/Captions";

import { FormProps } from "@/types/Common";

export default function Form({ type }: FormProps) {
  const getTypeStyles = (): StyleProp<ViewStyle> => {
    switch (type) {
      case 1:
        return { backgroundColor: "#51A42A" };
      case 2:
        return { backgroundColor: "#55585E" };
      case 3:
        return { backgroundColor: "#FF4C4C" };
      case "upcoming":
        return {
          borderWidth: 1,
          borderColor: "#454559",
          borderStyle: "dashed",
        };
      default:
        return {};
    }
  };

  const getText = () => {
    switch (type) {
      case 1:
        return "V";
      case 2:
        return "E";
      case 3:
        return "D";
      default:
        return null;
    }
  };

  return (
    <View style={[styles.form, getTypeStyles()]}>
      <Captions color={type === 2 ? "white" : "inverted"} highlight>
        {getText()}
      </Captions>
    </View>
  );
}

const styles = StyleSheet.create({
  form: {
    alignItems: "center",
    justifyContent: "center",
    width: 16,
    height: 16,
    borderRadius: 16,
  },
});
