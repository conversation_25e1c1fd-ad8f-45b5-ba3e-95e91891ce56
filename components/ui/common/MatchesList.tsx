import { StyleSheet, Text, View } from "react-native";

import Body from "@/components/typography/Body";

import { MatchesListItemProps, MatchesListProps } from "@/types/Common";

export default function MatchesList({ matches, total_odds }: MatchesListProps) {
  const isCombos = matches?.every(({ bet_odd }) => !bet_odd);

  return (
    <View style={[styles.matchesList, { gap: isCombos ? undefined : 12 }]}>
      {matches?.map((match, id) => (
        <MatchItem
          key={`analyzed_match_${id}`}
          {...match}
          total_odds={total_odds}
          isCombos={isCombos}
          index={id}
        />
      ))}
    </View>
  );
}

function MatchItem({
  fixture_home_team_name,
  fixture_away_team_name,
  bet_odd,
  bet_market_text,
  total_odds,
  isCombos,
  index,
}: MatchesListItemProps) {
  const splittedText = bet_market_text.split(" - ");

  return (
    <View>
      {(!isCombos || index === 0) && (
        <Text style={styles.matchItemSection} numberOfLines={1}>
          <Body highlight>{fixture_home_team_name} </Body>
          <Body color="secondary">vs </Body>
          <Body highlight>{fixture_away_team_name} </Body>
        </Text>
      )}
      {isCombos && index === 0 && (
        <Text style={styles.matchItemSection} numberOfLines={1}>
          <Body>Combos </Body>
          <Body color="primary" highlight>
            {Number(total_odds).toFixed(2)}
          </Body>
        </Text>
      )}
      <Text style={styles.matchItemSection} numberOfLines={1}>
        <Body color="secondary">{splittedText[0]}: </Body>
        <Body>{splittedText[1]} </Body>
        {!isCombos && (
          <Body color="primary" highlight>
            {Number(bet_odd).toFixed(2)}
          </Body>
        )}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  matchesList: {
    display: "flex",
    backgroundColor: "#171728",
    width: "100%",
    padding: 12,
    borderRadius: 8,
  },
  matchItemSection: {
    display: "flex",
    flexDirection: "row",
    gap: 6,
  },
});
