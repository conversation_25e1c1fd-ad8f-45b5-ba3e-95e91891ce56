import { StyleSheet, View } from "react-native";

import { Link } from "expo-router";

import Body from "@/components/typography/Body";
import Heading from "@/components/typography/Heading";

import Button from "../buttons/Button";

export default function GetApp() {
  return (
    <View style={styles.getApp}>
      <Heading style={styles.getAppTitle} variant="50">
        Get BetAnalyzer App
      </Heading>
      <Link href="https://play.google.com/store/apps" target="_blank">
        <Button onPress={() => null} withIcon={false}>
          <Body color="white" highlight>
            Download
          </Body>
        </Button>
      </Link>
    </View>
  );
}

const styles = StyleSheet.create({
  getApp: {
    position: "sticky",
    bottom: 8,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1E1E30",
    marginHorizontal: 8,
    marginBottom: 8,
    padding: 8,
    gap: 12,
    borderRadius: 16,
  },
  getAppTitle: {
    flexGrow: 1,
  },
});
