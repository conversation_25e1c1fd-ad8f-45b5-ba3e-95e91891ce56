import { Fragment, useEffect, useState } from "react";
import { Platform, StyleSheet, View } from "react-native";
import { GestureDetector, Gesture } from "react-native-gesture-handler";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";

import { BulletPointProps, VerticalListProps } from "@/types/Common";

export default function VerticalList({
  dataLength,
  type,
  children,
  scrollGesture,
}: VerticalListProps) {
  const [activeIndex, setActiveIndex] = useState(0);

  const translationX = useSharedValue(0);
  const prevTranslationX = useSharedValue(0);

  const animatedStyles = useAnimatedStyle(() => ({
    transform: [{ translateX: translationX.value }],
  }));

  const hasPagination = dataLength > 1;
  const isWeb = Platform.OS === "web";

  const pan = Gesture.Pan()
    .enabled(dataLength > 1)
    .blocksExternalGesture(scrollGesture)
    .failOffsetY([-1, 1])
    .minDistance(0)
    .onUpdate((event) => {
      const translateValue = prevTranslationX.value + event.translationX;

      if (translateValue > 0) {
        translationX.value = prevTranslationX.value + event.translationX / 10;
        return;
      }

      const rightEdge = (300 + 16) * (dataLength - 1);

      if (translateValue < -rightEdge) {
        translationX.value = prevTranslationX.value + event.translationX / 10;
        return;
      }

      translationX.value = translateValue;
    })
    .onEnd((event) => {
      if (event.translationX > 0) {
        const prevIndex = activeIndex > 0 ? activeIndex - 1 : 0;
        const finalTranslationValue = prevIndex * -(300 + 16);

        setActiveIndex(prevIndex);
        translationX.value = withTiming(finalTranslationValue);
        prevTranslationX.value = finalTranslationValue;
        return;
      }

      const nextIndex =
        activeIndex < dataLength - 1 ? activeIndex + 1 : dataLength - 1;
      const finalTranslationValue = nextIndex * -(300 + 16);

      setActiveIndex(nextIndex);
      translationX.value = withTiming(finalTranslationValue);
      prevTranslationX.value = finalTranslationValue;
    })
    .runOnJS(true);

  return (
    <Fragment>
      <GestureDetector gesture={pan}>
        <Animated.View
          style={[
            animatedStyles,
            isWeb ? styles.webStyles : undefined,
            { width: isWeb && !hasPagination ? "100%" : undefined },
          ]}
        >
          {children({ hasPagination, activeIndex })}
        </Animated.View>
      </GestureDetector>
      {hasPagination && (
        <View style={styles.pagination}>
          {Array.from({ length: dataLength }).map((_, id) => (
            <BulletPoint
              key={`vertical_list_bullet_point_${id}_${type}`}
              activeIndex={activeIndex}
              index={id}
            />
          ))}
        </View>
      )}
    </Fragment>
  );
}

function BulletPoint({ activeIndex, index }: BulletPointProps) {
  const size = useSharedValue(activeIndex === index ? 6 : 4);
  const color = useSharedValue(activeIndex === index ? "#FD4C21" : "#C5C5D3");

  useEffect(() => {
    size.value = withTiming(activeIndex === index ? 6 : 4);
    color.value = withTiming(activeIndex === index ? "#FD4C21" : "#C5C5D3");
  }, [activeIndex]);

  const animatedStyles = useAnimatedStyle(() => ({
    width: size.value,
    height: size.value,
    backgroundColor: color.value,
  }));

  return <Animated.View style={[styles.paginationItem, animatedStyles]} />;
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#121221",
    padding: 16,
    gap: 16,
  },
  heading: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    gap: 8,
  },
  list: {
    flexDirection: "row",
    gap: 16,
  },
  item: {
    flex: 1,
    gap: 16,
  },
  itemHeading: {
    flexDirection: "row",
    gap: 6,
  },
  keyPoint: {
    flexDirection: "row",
    gap: 8,
    maxWidth: "100%",
  },
  keyPointIcon: {
    transform: [{ rotate: "180deg" }],
  },
  keyPointText: {
    flex: 1,
    paddingVertical: 2,
  },
  pagination: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    minHeight: 6,
    gap: 4,
  },
  paginationItem: {
    width: 4,
    height: 4,
    borderRadius: 16,
  },
  webStyles: {
    alignSelf: "flex-start",
  },
});
