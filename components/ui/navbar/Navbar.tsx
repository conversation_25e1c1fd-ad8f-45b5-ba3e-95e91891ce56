import { Platform, Pressable, StyleSheet, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

import { useRouter } from "expo-router";

import Heading from "@/components/typography/Heading";

import InfoIcon from "@/components/icons/Info";
import ArrowLeftIcon from "@/components/icons/ArrowLeft";
import CaretDownIcon from "@/components/icons/CaretDown";

import { NavbarProps } from "@/types/Navbar";

import NavbarSkeleton from "./NavbarSkeleton";
import IconButton from "../buttons/IconButton";

export default function Navbar({
  label,
  onLabelClick,
  description,
  transparent = false,
  backIcon = <ArrowLeftIcon />,
  dismiss = false,
  infoIcon = <InfoIcon />,
  withInfo = false,
  scrollPosition = 0,
  loading,
}: NavbarProps) {
  const insets = useSafeAreaInsets();
  const router = useRouter();

  const isWeb = Platform.OS === "web";

  const handleBackBtn = () => {
    if (dismiss) {
      const canDismiss = router.canDismiss();

      canDismiss ? router.dismissAll() : router.replace("/");
      return;
    }

    router.back();
  };

  return (
    <View
      style={[
        styles.navbar,
        isWeb ? styles.webNavbar : undefined,
        {
          paddingTop: insets.top + 8,
          height: insets.top + 48,
          backgroundColor: transparent
            ? `rgba(18, 18, 33, ${scrollPosition / 100})`
            : "#121221",
          boxShadow: transparent
            ? `0px 2px 4px 0px rgba(0, 0, 0, ${
                (scrollPosition * 25) / 100 / 100
              })`
            : "0px 2px 4px 0px #00000040",
        },
      ]}
    >
      {loading ? (
        <NavbarSkeleton />
      ) : (
        <>
          <IconButton
            onPress={handleBackBtn}
            style={{
              opacity: !isWeb ? 1 : 0,
              pointerEvents: !isWeb ? "auto" : "none",
            }}
          >
            {backIcon}
          </IconButton>
          <View style={styles.content}>
            <Pressable style={styles.label} onPress={onLabelClick}>
              <Heading variant="200">{label}</Heading>
              {onLabelClick && <CaretDownIcon />}
            </Pressable>
            {description && description}
          </View>
          <IconButton
            style={{
              opacity: withInfo ? 1 : 0,
              pointerEvents: withInfo ? "auto" : "none",
            }}
            onPress={() => null}
          >
            {infoIcon}
          </IconButton>
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  navbar: {
    position: "relative",
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 8,
    gap: 4,
  },
  webNavbar: {
    alignSelf: "center",
    width: "100%",
    maxWidth: 375,
  },
  content: {
    display: "flex",
    alignItems: "center",
    flex: 1,
  },
  label: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
});
