import { Fragment } from "react";
import { Pressable, StyleSheet } from "react-native";

import Body from "@/components/typography/Body";

import LoadingIcon from "@/components/icons/Loading";

import { DropdownListItemProps } from "@/types/Dropdown";

export default function DropdownListItem({
  options,
  loading,
}: DropdownListItemProps) {
  return (
    <Fragment>
      {options.map(({ label, icon, onPress }, idx) => (
        <Pressable
          style={[styles.listItem, loading && styles.listItemLoading]}
          key={`upload_modal_option_${idx}`}
          onPress={onPress}
        >
          {loading ? <LoadingIcon /> : icon}
          <Body>{label}</Body>
        </Pressable>
      ))}
    </Fragment>
  );
}

const styles = StyleSheet.create({
  listItem: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    paddingTop: 6,
    paddingRight: 12,
    paddingBottom: 6,
    paddingLeft: 8,
  },
  listItemLoading: {
    opacity: 0.32,
    pointerEvents: "none",
  },
});
