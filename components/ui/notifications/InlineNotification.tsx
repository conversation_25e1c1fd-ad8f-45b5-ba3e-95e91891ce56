import { StyleSheet, View } from "react-native";

import Captions from "@/components/typography/Captions";

import InfoIcon from "@/components/icons/Info";

import { InlineNotificationProps } from "@/types/Notifications";

export default function InlineNotification({
  description,
}: InlineNotificationProps) {
  return (
    <View style={styles.notification}>
      <InfoIcon />
      <Captions style={styles.text} variant="200">
        {description}
      </Captions>
    </View>
  );
}

const styles = StyleSheet.create({
  notification: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1E1E30",
    gap: 8,
    paddingTop: 12,
    paddingRight: 8,
    paddingBottom: 12,
    paddingLeft: 8,
    borderRadius: 8,
  },
  text: {
    flex: 1,
  },
});
