import { useRef, useState } from "react";
import {
  Modal,
  View,
  StyleSheet,
  SafeAreaView,
  GestureResponderEvent,
  Platform,
} from "react-native";

import { ModalProps, ModalViewRef } from "@/types/Modal";

export default function BottomModal({
  visible,
  onClose,
  closeOutside = false,
  moveable = false,
  children,
}: ModalProps) {
  const [marginBottom, setMarginBottom] = useState(0);

  const modalRef = useRef<ModalViewRef | null>(null);
  const initialTouchRef = useRef(0);

  const handleTouchStart = ({ nativeEvent }: GestureResponderEvent) => {
    if (closeOutside) return;

    initialTouchRef.current = nativeEvent.pageY;
  };

  const handleTouchMove = ({ nativeEvent }: GestureResponderEvent) => {
    if (closeOutside) return;

    const moved = nativeEvent.pageY - initialTouchRef.current;

    if (moved > 0) {
      setMarginBottom(moved);
      return;
    }

    if (marginBottom === 0) return;

    setMarginBottom(0);
  };

  const handleTouchEnd = ({ target, nativeEvent }: GestureResponderEvent) => {
    if (closeOutside) {
      const webCondition = target === modalRef.current;
      const nativeCondition =
        nativeEvent.target === modalRef.current?.__nativeTag;

      if (Platform.OS === "web" ? webCondition : nativeCondition) {
        onClose?.();
      }

      return;
    }

    const moved = nativeEvent.pageY - initialTouchRef.current;

    if (moved <= 0) return;

    onClose?.();
    setMarginBottom(0);
    initialTouchRef.current = 0;
  };

  return (
    <Modal
      visible={visible}
      onRequestClose={onClose}
      animationType="slide"
      statusBarTranslucent
      navigationBarTranslucent
      transparent
    >
      <SafeAreaView style={Platform.OS === "web" ? styles.webModal : undefined}>
        <View
          ref={modalRef}
          style={[styles.modal, { marginBottom }]}
          onStartShouldSetResponder={() => moveable || closeOutside}
          onMoveShouldSetResponder={() => moveable}
          onResponderStart={handleTouchStart}
          onResponderMove={handleTouchMove}
          onResponderRelease={handleTouchEnd}
        >
          {children}
        </View>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modal: {
    height: "100%",
    backgroundColor: "#000000B2",
    justifyContent: "flex-end",
  },
  webModal: {
    width: "100%",
    height: "100%",
    alignSelf: "center",
    maxWidth: 375,
  },
});
