import { Image, StyleSheet, View } from "react-native";

import MatchesList from "../common/MatchesList";
import SheetHandleModal from "./SheetHandleModal";

import { ModalProps } from "@/types/Modal";
import { SlipBetsInfo, SlipInfoData } from "@/types/SlipInfoContext";

interface SlipPreviewModalProps
  extends Pick<ModalProps, "visible" | "onClose">,
    Pick<SlipInfoData, "image"> {
  matches: Array<SlipBetsInfo> | undefined;
  total_odds: string | null | undefined;
}

export default function SlipPreviewModal({
  matches,
  total_odds,
  image,
  visible,
  onClose,
}: SlipPreviewModalProps) {
  return (
    <SheetHandleModal visible={visible} onClose={onClose}>
      <View style={styles.content}>
        <MatchesList matches={matches} total_odds={total_odds} />
        {image && (
          <Image
            source={{ uri: image.uri }}
            style={styles.image}
            resizeMode="contain"
          />
        )}
      </View>
    </SheetHandleModal>
  );
}

const styles = StyleSheet.create({
  content: {
    width: "100%",
    alignItems: "center",
    paddingHorizontal: 16,
    gap: 16,
    marginTop: 4,
  },
  image: {
    width: "100%",
    aspectRatio: 1,
    borderRadius: 8,
  },
});
