import { Platform, StyleSheet, View } from "react-native";

import BottomModal from "./BottomModal";

import useNavigationBar from "@/hooks/useNavigationBar";

import { ModalProps } from "@/types/Modal";

export default function SheetHandleModal({
  visible,
  onClose,
  children,
}: ModalProps) {
  const { height } = useNavigationBar();

  const isWeb = Platform.OS === "web";

  return (
    <BottomModal
      visible={visible}
      onClose={onClose}
      moveable={true}
      closeOutside={isWeb}
    >
      <View
        style={[styles.container, { paddingBottom: 16 + (isWeb ? 0 : height) }]}
      >
        <View style={styles.sheetHandle} />
        {children}
      </View>
    </BottomModal>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    backgroundColor: "#121221",
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  sheetHandle: {
    backgroundColor: "#757588",
    width: 40,
    height: 4,
    borderRadius: 2,
    marginVertical: 8,
  },
});
