import { useEffect, useState } from "react";
import { Platform, Pressable, StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";

import { useRouter } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";

import { getFontFamily } from "@/helpers/font";

import { TabItemProps, TabsUIProps } from "@/types/Tabs";

import TabsSkeleton from "./TabsSkeleton";

export default function Tabs({ defaultTab, loading }: TabsUIProps) {
  const [selectedTab, setSelectedTab] = useState(
    defaultTab ? Number(defaultTab) : 0
  );

  const translateX = useSharedValue(100 * selectedTab);
  const router = useRouter();

  const tabsLength = 3;
  const isWeb = Platform.OS === "web";

  const animatedStyles = useAnimatedStyle(() => ({
    transform: [{ translateX: `${translateX.value}%` }],
  }));

  const handleSelectedTab = (newTab: number) => {
    if (newTab === selectedTab) return;

    router.setParams({ tab: newTab });
    translateX.value = withTiming(100 * newTab);
    setSelectedTab(newTab);
  };

  if (loading) return <TabsSkeleton />;

  return (
    <View style={[styles.tabs, isWeb ? styles.webTabs : undefined]}>
      {Array.from({ length: tabsLength }).map((_, id) => (
        <TabItem
          key={`tab_${id}`}
          onPress={handleSelectedTab}
          selectedTab={selectedTab}
          index={id}
        />
      ))}
      <Animated.View
        style={[
          styles.indicatorContainer,
          animatedStyles,
          { width: isWeb ? (375 - 32) / tabsLength : `${100 / tabsLength}%` }, // web -> maxWidth - padding / tabsLength
        ]}
      >
        <LinearGradient
          colors={["#FD4C21", "#B64F72"]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={[styles.indicator]}
        />
      </Animated.View>
    </View>
  );
}

function TabItem({ onPress, selectedTab, index }: TabItemProps) {
  const isActive = selectedTab === index;

  const color = useSharedValue(isActive ? "#C5C5D3" : "#757588");

  useEffect(() => {
    color.value = withTiming(isActive ? "#C5C5D3" : "#757588");
  }, [isActive]);

  const animatedStyles = useAnimatedStyle(() => ({
    color: color.value,
  }));

  return (
    <Pressable onPress={() => onPress(index)} style={styles.tabItem}>
      <Animated.Text
        style={[
          styles.tabText,
          animatedStyles,
          {
            fontFamily: getFontFamily(
              isActive ? "BaiJamjuree_700Bold" : "BaiJamjuree_500Medium"
            ),
          },
        ]}
      >
        Outcome {index}
      </Animated.Text>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  tabs: {
    position: "relative",
    flexDirection: "row",
    backgroundColor: "#121221",
    paddingHorizontal: 16,
    boxShadow: "0px 2px 4px 0px #00000040",
  },
  webTabs: {
    alignSelf: "center",
    width: "100%",
    maxWidth: 375,
  },
  tabItem: {
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
    height: 40,
  },
  tabText: {
    fontSize: 14,
    lineHeight: 20,
  },
  indicatorContainer: {
    position: "absolute",
    bottom: 0,
    left: 16,
  },
  indicator: {
    height: 3,
  },
});
