import { Platform, StyleSheet, View } from "react-native";

import SkeletonIcon from "@/components/icons/Skeleton";

export default function TabsSkeleton() {
  const isWeb = Platform.OS === "web";

  return (
    <View style={[styles.tabs, isWeb ? styles.webTabs : undefined]}>
      <View style={styles.tabItem}>
        <SkeletonIcon width={60} height={10} />
      </View>
      <View style={styles.tabItem}>
        <SkeletonIcon width={60} height={10} />
      </View>
      <View style={styles.tabItem}>
        <SkeletonIcon width={60} height={10} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  tabs: {
    position: "relative",
    flexDirection: "row",
    backgroundColor: "#121221",
    boxShadow: "0px 2px 4px 0px #00000040",
    paddingHorizontal: 16,
  },
  webTabs: {
    alignSelf: "center",
    width: "100%",
    maxWidth: 375,
  },
  tabItem: {
    alignItems: "center",
    justifyContent: "center",
    height: 40,
    flexGrow: 1,
  },
});
