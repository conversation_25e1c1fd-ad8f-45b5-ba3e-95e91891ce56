import { Fragment } from "react";
import { View } from "react-native";
import Svg, { Circle, Path } from "react-native-svg";

import SkeletonIcon from "@/components/icons/Skeleton";

interface ScoreSkeletonProps {
  size?: number;
  height?: number;
  strokeWidth?: number;
}

export default function ScoreSkeleton({
  size = 120,
  height = 100,
  strokeWidth = 8,
}: ScoreSkeletonProps) {
  const radius = size / 2 - strokeWidth;

  return (
    <Fragment>
      <View
        style={{
          position: "relative",
          width: size,
          height,
          overflow: "hidden",
        }}
      >
        <SkeletonIcon width={120} height={120} />
        <Svg
          width={size}
          height={size}
          viewBox={`0 0 ${size} ${size}`}
          style={{ position: "absolute" }}
        >
          <Circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            strokeLinecap="round"
            fill="#121221"
          />
          <Path
            d="M 0, 0 A 5 10 0 1 0 8, 0 A 5 10 0 0 1 0, 0"
            transform={`translate(14, 98), rotate(-35)`}
            fill="#121221"
          />
          <Path
            d="M 0, 0 A 5 10 0 1 0 8, 0 A 5 10 0 0 1 0, 0"
            transform={`translate(100, 94), rotate(35)`}
            fill="#121221"
          />
        </Svg>
      </View>
    </Fragment>
  );
}
