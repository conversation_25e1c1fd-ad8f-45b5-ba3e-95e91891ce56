import Svg, { Circle, Defs, LinearGradient, Stop } from "react-native-svg";

export default function Progress({
  progress = 0,
  size = 104,
  strokeWidth = 8,
}) {
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const dashOffset = circumference * (1 - progress / 100);

  return (
    <Svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
      <Defs>
        <LinearGradient id="progress" x1="100%" x2="0%" y1="100%" y2="0%">
          <Stop offset="0%" stopColor="#FD4C21" />
          <Stop offset="100%" stopColor="#B64F72" />
        </LinearGradient>
      </Defs>
      <Circle
        cx={size / 2}
        cy={size / 2}
        r={radius}
        stroke="url(#progress)"
        strokeWidth={strokeWidth}
        fill="none"
        strokeDasharray={circumference}
        strokeDashoffset={dashOffset}
        transform={`rotate(-90 ${size / 2} ${size / 2})`}
      />
      <Circle
        cx={size / 2}
        cy={size / 2}
        r={radius}
        stroke="#0F0F1B"
        strokeWidth={strokeWidth}
        fill="none"
        strokeDasharray="2"
        transform={`rotate(-90 ${size / 2} ${size / 2})`}
      />
    </Svg>
  );
}
