import { Fragment } from "react";
import { Platform } from "react-native";
import Svg, { Circle, Defs, Polygon, Use } from "react-native-svg";

interface ScoreProps {
  score: number;
  color: string | undefined;
  indicatorColor: string | undefined;
  size?: number;
  height?: number;
  strokeWidth?: number;
}

export default function Score({
  score = 1,
  size = 120,
  height = 100,
  strokeWidth = 8,
  color,
  indicatorColor,
}: ScoreProps) {
  const isWeb = Platform.OS === "web";

  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const chordHeight = size - height;
  const chordLength =
    2 * Math.sqrt(2 * radius * chordHeight - Math.pow(chordHeight, 2));
  const centerAngle = Math.asin(chordLength / (radius * 2)) * 2;
  const centerAngleInDegrees = centerAngle * (180 / Math.PI);
  const arcLength = centerAngle * radius;
  const coloredArcLength =
    arcLength + ((circumference - arcLength) / 10) * (10 - score);

  const indicatorHeight = 17.5;
  const indicatorWidth = 7;
  const indicatorStroke = 2;
  const initialPointAngle = 90 + 90 + centerAngleInDegrees / 2;
  const additionalColorArcLength = ((circumference - arcLength) / 10) * score;
  const additionalColorAngle =
    (additionalColorArcLength / radius) * (180 / Math.PI);

  const getTransform = () => {
    if (isWeb)
      return `rotate(${initialPointAngle + additionalColorAngle} ${
        indicatorWidth / 2
      } ${0}), translate(0, ${-(size / 2 - indicatorHeight / 2 - 4)})`;

    return `rotate(${initialPointAngle + additionalColorAngle} ${
      indicatorWidth / 2
    } ${indicatorHeight / 2}), translate(0, ${
      -size / 2 + indicatorHeight / 2 + 4
    })`;
  };

  return (
    <Fragment>
      <Svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
        <Circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="#131324"
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          fill="none"
          strokeDasharray={circumference}
          strokeDashoffset={arcLength}
          transform={`rotate(${90 + centerAngleInDegrees / 2} ${size / 2} ${
            size / 2
          })`}
        />
        <Circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          fill="none"
          strokeDasharray={circumference}
          strokeDashoffset={coloredArcLength}
          transform={`rotate(${90 + centerAngleInDegrees / 2} ${size / 2} ${
            size / 2
          })`}
        />
      </Svg>
      <Svg
        width={size + indicatorHeight}
        height={size + indicatorHeight}
        viewBox={`0 0 ${size} ${size}`}
        style={{
          position: "absolute",
          top: -indicatorHeight,
        }}
      >
        {isWeb ? (
          <>
            <Defs>
              <Polygon
                id="indicator"
                points="0,0 0,14.5 3.5,17.5 7,14.5 7,0"
                fill={indicatorColor}
                stroke="#1E1523"
                strokeWidth={indicatorStroke}
                x={size / 2 - indicatorWidth / 2}
                y={size / 2 - 1}
                transform={getTransform()}
              />
            </Defs>
            <Use
              x={size / 2 - indicatorWidth / 2}
              y={size / 2 - 1}
              xlinkHref="#indicator"
            />
          </>
        ) : (
          <Polygon
            points="0,0 0,14.5 3.5,17.5 7,14.5 7,0"
            fill={indicatorColor}
            stroke="#1E1523"
            strokeWidth={indicatorStroke}
            x={size / 2 - indicatorWidth / 2}
            y={size / 2 - 1}
            transform={getTransform()}
          />
        )}
      </Svg>
    </Fragment>
  );
}
