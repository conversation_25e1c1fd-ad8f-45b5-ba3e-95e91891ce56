import { Pressable, StyleSheet } from "react-native";

import { LinearGradient } from "expo-linear-gradient";

import LoadingIcon from "@/components/icons/Loading";

import { ButtonIconProps, ButtonProps } from "@/types/Buttonts";

export default function Button({
  children,
  onPress,
  variant = "primary",
  withIcon = true,
  icon,
  loading,
}: ButtonProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case "secondary":
        return { backgroundColor: "#1E1E30" };
      default: // primary
        return undefined;
    }
  };

  return (
    <Pressable
      style={[
        variant !== "primary"
          ? [
              styles.button,
              withIcon ? styles.buttonWithIcon : styles.buttonWithoutIcon,
            ]
          : undefined,

        loading && styles.buttonLoading,
        getVariantStyles(),
      ]}
      onPress={onPress}
    >
      {variant === "primary" ? (
        <LinearGradient
          colors={["#FD4C21", "#B64F72"]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={[
            styles.button,
            withIcon ? styles.buttonWithIcon : styles.buttonWithoutIcon,
          ]}
        >
          <ButtonIcon icon={icon} loading={loading} />
          {children}
        </LinearGradient>
      ) : (
        <>
          <ButtonIcon icon={icon} loading={loading} />
          {children}
        </>
      )}
    </Pressable>
  );
}

function ButtonIcon({ icon, loading }: ButtonIconProps) {
  if (!icon) return null;

  return loading ? <LoadingIcon /> : icon;
}

const styles = StyleSheet.create({
  button: {
    display: "flex",
    flexDirection: "row",
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 20,
    gap: 4,
  },
  buttonLoading: {
    opacity: 0.32,
    pointerEvents: "none",
  },
  buttonWithIcon: {
    paddingVertical: 4,
    paddingRight: 12,
    paddingLeft: 4,
  },
  buttonWithoutIcon: {
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
});
