import { StyleSheet, Text } from "react-native";

import { TypographyProps } from "@/types/Typography";

import { getFontFamily, getFontWeight } from "@/helpers/font";

export default function Heading({
  children,
  style,
  variant = "100",
  align = "left",
  onPress,
}: TypographyProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case "400":
        return {
          fontSize: 28,
          lineHeight: 34,
        };
      case "300":
        return {
          fontSize: 20,
          lineHeight: 26,
        };
      case "200":
        return {
          fontSize: 16,
          lineHeight: 22,
        };
      case "50":
        return {
          fontSize: 14,
          lineHeight: 20,
        };
      default: // 100
        return {
          fontSize: 12,
          lineHeight: 18,
        };
    }
  };

  return (
    <Text
      style={[getVariantStyles(), styles.heading, { textAlign: align }, style]}
      onPress={onPress}
    >
      {children}
    </Text>
  );
}

const styles = StyleSheet.create({
  heading: {
    fontFamily: getFontFamily("BaiJamjuree_700Bold"),
    fontWeight: getFontWeight("BaiJamjuree_700Bold"),
    color: "#C5C5D3",
  },
});
