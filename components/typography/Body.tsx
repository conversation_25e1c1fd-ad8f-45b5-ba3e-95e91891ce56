import { Text } from "react-native";

import { TypographyProps } from "@/types/Typography";

import { getFontFamily, getFontWeight } from "@/helpers/font";

export default function Body({
  children,
  variant = "100",
  color,
  align = "left",
  highlight = false,
  style,
}: TypographyProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case "200":
        return {
          fontSize: 16,
          lineHeight: 22,
        };
      default: // 100
        return {
          fontSize: 14,
          lineHeight: 20,
        };
    }
  };

  const getColorStyles = () => {
    switch (color) {
      case "white":
        return { color: "#FFFFFF" };
      case "secondary":
        return { color: "#757588" };
      case "primary":
        return { color: "#FD4C21" };
      default:
        return { color: "#C5C5D3" };
    }
  };

  return (
    <Text
      style={[
        style,
        {
          textAlign: align,
          fontFamily: highlight
            ? getFontFamily("BaiJamjuree_700Bold")
            : getFontFamily("BaiJamjuree_400Regular"),
          fontWeight: highlight
            ? getFontWeight("BaiJamjuree_700Bold")
            : getFontWeight("BaiJamjuree_400Regular"),
        },
        getVariantStyles(),
        getColorStyles(),
      ]}
    >
      {children}
    </Text>
  );
}
