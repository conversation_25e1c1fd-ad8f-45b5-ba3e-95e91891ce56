import { Text } from "react-native";

import { TypographyProps } from "@/types/Typography";

import { getFontFamily, getFontWeight } from "@/helpers/font";

export default function Captions({
  children,
  style,
  variant = "100",
  color,
  align = "left",
  highlight = false,
  ...props
}: TypographyProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case "200":
        return { fontSize: 12, lineHeight: 18 };
      default: // 100
        return { fontSize: 10, lineHeight: 12 };
    }
  };

  const getColorStyles = () => {
    switch (color) {
      case "disabled":
        return { color: "#454559" };
      case "inverted":
        return { color: "#1C1D21" };
      case "white":
        return { color: "#FFFFFF" };
      case "secondary":
        return { color: "#757588" };
      default:
        return { color: "#C5C5D3" };
    }
  };

  return (
    <Text
      style={[
        style,
        {
          textAlign: align,
          fontFamily: highlight
            ? getFontFamily("BaiJamjuree_700Bold")
            : getFontFamily("BaiJamjuree_500Medium"),
          fontWeight: highlight
            ? getFontWeight("BaiJamjuree_700Bold")
            : getFontWeight("BaiJamjuree_500Medium"),
        },
        getVariantStyles(),
        getColorStyles(),
      ]}
      {...props}
    >
      {children}
    </Text>
  );
}
