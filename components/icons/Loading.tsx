import { useEffect } from "react";
import Svg, { Path } from "react-native-svg";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withTiming,
} from "react-native-reanimated";

import { IconProps } from "@/types/Icon";

const AnimatedSvg = Animated.createAnimatedComponent(Svg);

export default function LoadingIcon({
  size = 24,
  color = "#C5C5D3",
}: IconProps) {
  const rotate = useSharedValue(0);

  useEffect(() => {
    const totalDuration = 1000;
    const numberOfSteps = 8;
    const stepPercentage = 100 / numberOfSteps;
    const animationDuration = 0.001;
    const pausedDuration =
      (stepPercentage / 100 - animationDuration) * totalDuration;
    const stepDuration = animationDuration * totalDuration;

    rotate.value = withRepeat(
      withSequence(
        withTiming(0, { duration: pausedDuration }),
        withTiming(45, { duration: stepDuration }),
        withTiming(45, { duration: pausedDuration }),
        withTiming(90, { duration: stepDuration }),
        withTiming(90, { duration: pausedDuration }),
        withTiming(135, { duration: stepDuration }),
        withTiming(135, { duration: pausedDuration }),
        withTiming(180, { duration: stepDuration }),
        withTiming(180, { duration: pausedDuration }),
        withTiming(225, { duration: stepDuration }),
        withTiming(225, { duration: pausedDuration }),
        withTiming(270, { duration: stepDuration }),
        withTiming(270, { duration: pausedDuration }),
        withTiming(315, { duration: stepDuration }),
        withTiming(315, { duration: pausedDuration }),
        withTiming(360, { duration: stepDuration })
      ),
      -1
    );
  }, []);

  const animatedStyles = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotate.value}deg` }],
  }));

  return (
    <AnimatedSvg
      style={animatedStyles}
      width={size}
      height={size}
      viewBox="0 0 24 24"
    >
      <Path fill={color} d="M11 2h2v5h-2V2Z" />
      <Path
        fill={color}
        d="m18.364 4.222 1.414 1.414-3.535 3.536-1.415-1.415 3.536-3.535Z"
        opacity={0.85}
      />
      <Path fill={color} d="M22 11v2h-5v-2h5Z" opacity={0.65} />
      <Path
        fill={color}
        d="m19.778 18.364-1.414 1.414-3.536-3.535 1.415-1.415 3.535 3.536Z"
        opacity={0.45}
      />
      <Path fill={color} d="M13 22h-2v-5h2v5Z" opacity={0.3} />
      <Path
        fill={color}
        d="m5.636 19.778-1.414-1.414 3.535-3.536 1.415 1.415-3.536 3.535Z"
        opacity={0.2}
      />
      <Path fill={color} d="M2 13v-2h5v2H2Z" opacity={0.1} />
      <Path
        fill={color}
        d="m4.222 5.636 1.414-1.414 3.536 3.535-1.415 1.415-3.535-3.536Z"
        opacity={0.07}
      />
    </AnimatedSvg>
  );
}
