import Svg, { LinearGradient, Defs, Path, Stop } from "react-native-svg";

import { IconProps } from "@/types/Icon";

export default function BulbIcon({
  size = 24,
  color = "#C5C5D3",
  style,
}: IconProps) {
  return (
    <Svg style={style} width={size} height={size} viewBox="0 0 24 24">
      <Defs>
        <LinearGradient
          id="extraction_gradient"
          x1="0%"
          x2="100%"
          y1="0%"
          y2="100%"
        >
          <Stop offset="0%" stopColor="#FD4C21" />
          <Stop offset="100%" stopColor="#B64F72" />
        </LinearGradient>
      </Defs>
      <Path
        fill={color === "gradient" ? "url(#extraction_gradient)" : color}
        fillRule="evenodd"
        d="M13 1v3h-2V1h2ZM4.9 3.486 6.914 5.5 5.5 6.914 3.486 4.9 4.9 3.486ZM20.515 4.9 18.5 6.914 17.086 5.5 19.1 3.486 20.515 4.9ZM5 12a7 7 0 1 1 11.5 5.362v1.477c0 .527 0 .982-.03 1.356-.033.395-.104.789-.297 1.167a3 3 0 0 1-1.311 1.311c-.378.193-.772.264-1.167.296-.375.031-.83.031-1.356.031h-.678c-.527 0-.981 0-1.356-.03-.395-.033-.789-.104-1.167-.297a3 3 0 0 1-1.311-1.311c-.193-.378-.264-.772-.296-1.167a17.9 17.9 0 0 1-.031-1.357v-1.476A6.987 6.987 0 0 1 5 12Zm7-5a5 5 0 0 0-2.916 9.062 1 1 0 0 1 .416.812V18.8c0 .577 0 .949.024 1.232.022.272.06.372.085.422a1 1 0 0 0 .437.437c.*************.422.085.283.023.655.024 1.232.024h.6c.577 0 .949 0 1.232-.024.272-.022.373-.06.422-.085a1 1 0 0 0 .437-.437c.025-.05.063-.15.085-.422.023-.283.024-.655.024-1.232v-1.926a1 1 0 0 1 .416-.812A5 5 0 0 0 12 7ZM1 11h3v2H1v-2Zm19 0h3v2h-3v-2ZM9 12.5h6v2h-2v5h-2v-5H9v-2Z"
        clipRule="evenodd"
      />
    </Svg>
  );
}
