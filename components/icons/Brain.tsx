import Svg, { LinearGradient, Defs, Path, Stop } from "react-native-svg";

import { IconProps } from "@/types/Icon";

export default function BrainIcon({
  size = 24,
  color = "#C5C5D3",
  style,
}: IconProps) {
  return (
    <Svg style={style} width={size} height={size} viewBox="0 0 24 24">
      <Defs>
        <LinearGradient
          id="extraction_gradient"
          x1="0%"
          x2="100%"
          y1="0%"
          y2="100%"
        >
          <Stop offset="0%" stopColor="#FD4C21" />
          <Stop offset="100%" stopColor="#B64F72" />
        </LinearGradient>
      </Defs>
      <Path
        fill={color === "gradient" ? "url(#extraction_gradient)" : color}
        d="M9.32 8.821a4.256 4.256 0 0 0-.108-2.282l-1.89.655c.087.25.158.713.05 1.172-.097.418-.341.848-.937 1.159l.926 1.773c1.165-.608 1.743-1.552 1.96-2.477ZM9.46 17.162a4.255 4.255 0 0 1-2.03-1.047c-.694-.65-1.221-1.622-1.166-2.935l1.998.085c-.028.671.222 1.097.535 1.391.344.322.78.492 1.04.542l-.377 1.964ZM14.547 8.821a4.256 4.256 0 0 1 .109-2.282l1.89.655c-.088.25-.158.713-.051 1.172.098.418.342.848.938 1.159l-.926 1.773c-1.165-.608-1.744-1.552-1.96-2.477ZM14.408 17.162a4.255 4.255 0 0 0 2.03-1.047c.693-.65 1.221-1.622 1.166-2.935l-1.999.085c.029.671-.221 1.097-.534 1.391-.344.322-.78.492-1.04.542l.377 1.964Z"
      />
      <Path
        fill={color === "gradient" ? "url(#extraction_gradient)" : color}
        fillRule="evenodd"
        d="M12 2.497a4.157 4.157 0 0 0-2.26-1.004 3.323 3.323 0 0 0-2.177.504c-.57.369-1.036.918-1.376 1.634-1.582.579-2.95 2.218-2.644 4.433a5.19 5.19 0 0 0-1.991 3.776c-.084 1.51.533 3.039 1.956 4.094-.125.676-.123 1.448.165 2.199.374.976 1.17 1.759 2.419 2.222.4 1.066 1.434 1.84 2.537 2.084 1.116.247 2.347-.015 3.371-.938 1.024.923 2.255 1.185 3.37.938 1.104-.243 2.138-1.018 2.538-2.084 1.25-.463 2.045-1.246 2.42-2.223.287-.75.29-1.522.164-2.198 1.423-1.055 2.04-2.584 1.956-4.094a5.19 5.19 0 0 0-1.99-3.776c.306-2.215-1.063-3.854-2.645-4.433-.34-.716-.805-1.265-1.376-1.634a3.324 3.324 0 0 0-2.177-.504A4.157 4.157 0 0 0 12 2.497Zm-4.134 2.3c.228-.63.525-.954.783-1.12.258-.168.556-.229.88-.195.639.068 1.218.48 1.471.887v15.28c-.657.84-1.392.959-1.94.837-.708-.156-1.104-.672-1.14-1.018l-.07-.688-.668-.177c-1.095-.29-1.487-.783-1.642-1.186-.177-.463-.132-1.046.064-1.598l.285-.803-.749-.409c-1.212-.66-1.645-1.681-1.591-2.656a3.187 3.187 0 0 1 1.603-2.563l.693-.39-.224-.762c-.459-1.565.518-2.593 1.509-2.8l.546-.114.19-.525Zm8.268 0c-.228-.63-.525-.954-.783-1.12a1.325 1.325 0 0 0-.88-.195c-.639.068-1.218.48-1.471.887v15.28c.657.84 1.392.959 1.94.837.708-.156 1.104-.672 1.14-1.018l.07-.688.668-.177c1.095-.29 1.487-.783 1.642-1.186.177-.463.132-1.046-.064-1.598l-.285-.803.749-.409c1.212-.66 1.645-1.681 1.591-2.656a3.187 3.187 0 0 0-1.603-2.563l-.693-.39.224-.762c.459-1.565-.518-2.593-1.509-2.8l-.546-.114-.19-.525Z"
        clipRule="evenodd"
      />
    </Svg>
  );
}
