import { Svg, Path } from "react-native-svg";

import { IconProps } from "@/types/Icon";

export default function InfoIcon({ color = "#C2C6D0" }: IconProps) {
  return (
    <Svg width={24} height={24} viewBox="0 0 24 24">
      <Path
        fill={color}
        d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10Zm0-2a8 8 0 1 0 0-16.001A8 8 0 0 0 12 20ZM11 7h2v2h-2V7Zm0 4h2v6h-2v-6Z"
      />
    </Svg>
  );
}
