import Svg, { LinearGradient, Defs, Path, Stop } from "react-native-svg";

import { IconProps } from "@/types/Icon";

export default function CheckmarkIcon({
  size = 24,
  color = "#C5C5D3",
  style,
}: IconProps) {
  return (
    <Svg style={style} width={size} height={size} viewBox="0 0 24 24">
      <Defs>
        <LinearGradient
          id="extraction_gradient"
          x1="0%"
          x2="100%"
          y1="0%"
          y2="100%"
        >
          <Stop offset="0%" stopColor="#FD4C21" />
          <Stop offset="100%" stopColor="#B64F72" />
        </LinearGradient>
      </Defs>
      <Path
        fill={color === "gradient" ? "url(#extraction_gradient)" : color}
        fillRule="evenodd"
        d="m18.5 8.415-8.297 8.292L5.5 12.006l1.414-1.415 3.29 3.288L17.085 7 18.5 8.415Z"
        clipRule="evenodd"
      />
    </Svg>
  );
}
