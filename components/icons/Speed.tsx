import Svg, { LinearGradient, Defs, Path, Stop } from "react-native-svg";

import { IconProps } from "@/types/Icon";

export default function SpeedIcon({
  size = 24,
  color = "#C5C5D3",
  style,
}: IconProps) {
  return (
    <Svg style={style} width={size} height={size} viewBox="0 0 24 24">
      <Defs>
        <LinearGradient id="speed_gradient" x1="0%" x2="100%" y1="0%" y2="100%">
          <Stop offset="0%" stopColor="#FD4C21" />
          <Stop offset="100%" stopColor="#B64F72" />
        </LinearGradient>
      </Defs>
      <Path
        fill={color === "gradient" ? "url(#speed_gradient)" : color}
        d="M22 13c0-5.523-4.477-10-10-10S2 7.477 2 13a9.97 9.97 0 0 0 2.973 7.115l1.399-1.43a8 8 0 1 1 11.283-.026l1.427 1.4A9.968 9.968 0 0 0 22 13Z"
      />
      <Path
        fill={color === "gradient" ? "url(#speed_gradient)" : color}
        d="M14.858 6.608a7 7 0 0 0-7.964 11.181l.156.161 1.414-1.414a5 5 0 0 1 4.831-8.366l1.563-1.562ZM19 13a6.985 6.985 0 0 0-.608-2.857l-1.562 1.562a4.992 4.992 0 0 1-1.294 4.831l1.414 1.414A6.982 6.982 0 0 0 19 13Z"
      />
      <Path
        fill={color === "gradient" ? "url(#speed_gradient)" : color}
        d="m17.657 8.757-1.415-1.414-3.725 3.724A2.002 2.002 0 0 0 10 13a2 2 0 1 0 3.933-.517l3.724-3.726Z"
      />
    </Svg>
  );
}
