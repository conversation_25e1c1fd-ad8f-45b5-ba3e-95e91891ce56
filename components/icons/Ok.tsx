import Svg, { Path } from "react-native-svg";

import { IconProps } from "@/types/Icon";

export default function OkIcon({ size = 24, color = "#C5C5D3" }: IconProps) {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24">
      <Path
        fill={color}
        fillRule="evenodd"
        d="M13.277 2.763a.648.648 0 0 0-1.216.451L13.7 7.65c.52.133 1.042.316 1.563.55l-.82 1.825c-2.108-.947-4.002-.782-5.373.172-.315.218-.397.419-.418.537a.587.587 0 0 0 .1.428c.098.148.24.25.382.288.124.034.28.033.475-.087.417-.256.968-.412 1.506-.481a5.217 5.217 0 0 1 1.802.074c.613.14 1.263.427 1.767.963.52.555.809 1.297.809 2.188 0 .739-.176 1.381-.515 1.91a3.05 3.05 0 0 1-1.283 1.111c-.93.44-1.99.43-2.707.255l-.247-.06-2.131-1.946.014-.46c.017-.501-.1-.82-.241-1.033-.146-.222-.364-.399-.647-.536A3.04 3.04 0 0 0 7 13.11v2.808l3 4.312V23H8v-2.143l-3-4.312V11.11l.931-.064c.183-.013.433-.016.724.005-.02-.22-.012-.446.03-.675.13-.71.559-1.345 1.244-1.821a6.516 6.516 0 0 1 3.546-1.158l-1.29-3.49a2.648 2.648 0 0 1 4.966-1.841l.062.166A2.5 2.5 0 0 1 19 4.376V18.54l-2 2.33V23h-2v-2.87l2-2.33v-5.026l-3.723-10.01ZM17 7.037V4.376a.5.5 0 0 0-1-.026l1 2.687Zm-6.69 6.213c.116-.051.232-.112.347-.183.092-.057.34-.153.716-.202.36-.047.753-.038 *********.08.599.218.754.383.137.147.267.383.267.82 0 .413-.096.668-.2.83a1.052 1.052 0 0 1-.452.382c-.351.165-.797.202-1.151.16l-1.076-.981a3.69 3.69 0 0 0-.305-1.25Z"
        clipRule="evenodd"
      />
    </Svg>
  );
}
