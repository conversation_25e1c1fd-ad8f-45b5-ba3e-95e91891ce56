import Svg, { Path } from "react-native-svg";

import { IconProps } from "@/types/Icon";

export default function ShareIcon({
  size = 24,
  color = "#C5C5D3",
  style,
}: IconProps) {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" style={style}>
      <Path fill={color} d="M13 6h3l-4-5-4 5h3v8h2V6Z" />
      <Path
        fill={color}
        fillRule="evenodd"
        d="M3 10a2 2 0 0 1 2-2h4v2H5v9h14v-9h-4V8h4a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-9Z"
        clipRule="evenodd"
      />
    </Svg>
  );
}
