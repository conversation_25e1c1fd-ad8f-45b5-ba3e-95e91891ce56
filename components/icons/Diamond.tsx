import Svg, { LinearGradient, Defs, Path, Stop } from "react-native-svg";

import { IconProps } from "@/types/Icon";

export default function DiamondIcon({
  size = 24,
  color = "#C5C5D3",
  style,
}: IconProps) {
  return (
    <Svg style={style} width={size} height={size} viewBox="0 0 24 24">
      <Defs>
        <LinearGradient id="speed_gradient" x1="0%" x2="100%" y1="0%" y2="100%">
          <Stop offset="0%" stopColor="#FD4C21" />
          <Stop offset="100%" stopColor="#B64F72" />
        </LinearGradient>
      </Defs>
      <Path
        fill={color === "gradient" ? "url(#speed_gradient)" : color}
        fillRule="evenodd"
        d="M19.127 3H4.873a1 1 0 0 0-.809.412L.241 8.668a.5.5 0 0 0 .037.633l3.67 3.976c5.137 5.564 7.707 8.348 7.713 8.353a.5.5 0 0 0 .706-.028L23.722 9.301a.5.5 0 0 0 .037-.633l-3.823-5.256A1 1 0 0 0 19.127 3ZM3.201 8l2.182-3h3.14l-1.2 3H3.201Zm4.088 2H3.645l5.847 6.333L7.289 10ZM12 17.456 9.407 10h5.186L12 17.456ZM9.477 8l1.2-3h2.646l1.2 3H9.477Zm7.2 0-1.2-3h3.14l2.182 3h-4.122Zm.034 2-2.203 6.334L20.355 10h-3.644Z"
        clipRule="evenodd"
      />
    </Svg>
  );
}
