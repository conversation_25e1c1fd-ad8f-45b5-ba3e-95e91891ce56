import Svg, { Path } from "react-native-svg";

import { IconProps } from "@/types/Icon";

export default function UsersIcon({ size = 16, color = "#C5C5D3" }: IconProps) {
  return (
    <Svg width={size} height={size} viewBox="0 0 16 16">
      <Path
        fill={color}
        d="M4.5 7.158c-.414 0-.823-.08-1.205-.234a3.156 3.156 0 0 1-1.022-.668 3.075 3.075 0 0 1-.683-.999 3.019 3.019 0 0 1 0-2.356c.158-.374.39-.713.683-1 .292-.285.64-.512 1.022-.667A3.213 3.213 0 0 1 4.5 1c.835 0 1.637.324 2.227.902.591.577.923 1.36.923 2.177s-.332 1.6-.923 2.177a3.187 3.187 0 0 1-2.227.902Zm7.35 2.737a2.833 2.833 0 0 1-1.98-.802 2.706 2.706 0 0 1-.82-1.935c0-.726.295-1.422.82-1.935a2.833 2.833 0 0 1 1.98-.802c.743 0 1.455.288 1.98.802.525.513.82 1.209.82 1.935s-.295 1.422-.82 1.935a2.833 2.833 0 0 1-1.98.802Zm0 .684c.835 0 1.637.324 2.227.902.591.577.923 1.36.923 2.177V14H8.7v-.342c0-.817.332-1.6.923-2.177a3.187 3.187 0 0 1 2.227-.902ZM4.5 7.842c.46 0 .915.089 1.34.26.424.172.81.424 1.135.742.325.318.583.695.759 1.11.175.415.266.86.266 1.31V14H1v-2.737c0-.907.369-1.777 1.025-2.419A3.541 3.541 0 0 1 4.5 7.842Z"
      />
    </Svg>
  );
}
