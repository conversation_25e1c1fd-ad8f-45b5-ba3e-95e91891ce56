import Svg, { Path } from "react-native-svg";

import { IconProps } from "@/types/Icon";

export default function Speed1Icon({
  size = 24,
  color = "#C5C5D3",
}: IconProps) {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24">
      <Path
        fill={color}
        fillRule="evenodd"
        d="M3.055 11H9.17A3.001 3.001 0 0 1 15 12a3 3 0 0 1-5.83 1H3.056a8.957 8.957 0 0 0 1.915 4.62c.384-.374.708-.659 1.074-.883a5.002 5.002 0 0 1 1.446-.599C8.067 16 8.668 16 9.537 16h4.926c.869 0 1.47 0 2.047.138.51.123.998.325 1.446.599.366.224.69.51 1.074.883A8.957 8.957 0 0 0 20.945 13H18.5v-2h2.445a8.958 8.958 0 0 0-1.907-4.61l-1.67 1.523-1.348-1.478 1.604-1.462A8.957 8.957 0 0 0 13 3.055V5.5h-2V3.055a8.957 8.957 0 0 0-4.624 1.918L7.98 6.435 6.632 7.913 4.962 6.39A8.957 8.957 0 0 0 3.055 11Zm1.584-7.174A10.972 10.972 0 0 0 1 12c0 2.678.957 5.132 2.547 7.04l-.04.038 1.415 1.415.039-.04A10.955 10.955 0 0 0 12 23c2.678 0 5.132-.957 7.04-2.547l.038.04 1.415-1.415-.04-.039A10.956 10.956 0 0 0 23 12c0-3.244-1.404-6.16-3.638-8.174l-.218-.239-.027.025A10.956 10.956 0 0 0 12 1c-2.714 0-5.198.983-7.117 2.612l-.027-.025-.217.239Zm12.976 15.208c-.332-.321-.512-.474-.704-.592a3 3 0 0 0-.867-.36c-.319-.076-.669-.082-1.695-.082H9.651c-1.026 0-1.376.006-1.694.083a3 3 0 0 0-.868.36c-.192.117-.372.27-.704.591A8.962 8.962 0 0 0 12 21a8.961 8.961 0 0 0 5.615-1.966ZM12 11a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z"
        clipRule="evenodd"
      />
    </Svg>
  );
}
