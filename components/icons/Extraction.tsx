import Svg, { LinearGradient, Defs, Path, Stop } from "react-native-svg";

import { IconProps } from "@/types/Icon";

export default function ExtractionIcon({
  size = 24,
  color = "#C5C5D3",
  style,
}: IconProps) {
  return (
    <Svg style={style} width={size} height={size} viewBox="0 0 24 24">
      <Defs>
        <LinearGradient
          id="extraction_gradient"
          x1="0%"
          x2="100%"
          y1="0%"
          y2="100%"
        >
          <Stop offset="0%" stopColor="#FD4C21" />
          <Stop offset="100%" stopColor="#B64F72" />
        </LinearGradient>
      </Defs>
      <Path
        fill={color === "gradient" ? "url(#extraction_gradient)" : color}
        d="M21 16v5H3v-5h2v3h14v-3h2ZM3 11h18v2H3v-2Zm18-3h-2V5H5v3H3V3h18v5Z"
      />
    </Svg>
  );
}
