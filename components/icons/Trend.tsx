import Svg, { Path } from "react-native-svg";

import { IconProps } from "@/types/Icon";

export default function TrendIcon({ size = 24, color = "#C5C5D3" }: IconProps) {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24">
      <Path
        fill={color}
        d="m4.406 14.523 3.402-3.402 2.828 2.829 3.157-3.157L12 9h5v5l-1.793-1.793-4.571 4.571-2.828-2.828-2.475 2.474a8 8 0 1 0-.927-1.9v-.001Zm-1.538 1.558-.01-.01.004-.004A9.965 9.965 0 0 1 2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10c-4.07 0-7.57-2.43-9.132-5.919Z"
      />
    </Svg>
  );
}
