import { useEffect } from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

import { LinearGradient } from "expo-linear-gradient";

import { SkeletonIconProps } from "@/types/Icon";

export default function SkeletonIcon({
  width,
  height,
  radius = 60,
  style,
}: SkeletonIconProps) {
  const translateX = useSharedValue(-66);

  useEffect(() => {
    translateX.value = withRepeat(withTiming(0, { duration: 1000 }), -1);
  }, []);

  const animatedStyles = useAnimatedStyle(() => ({
    transform: [{ translateX: `${translateX.value}%` }],
  }));

  return (
    <View
      style={[
        styles.skeletonContainer,
        {
          width,
          height,
          borderRadius: radius,
        },
        style,
      ]}
    >
      <Animated.View
        style={[
          styles.skeletonGradientContainer,
          {
            borderRadius: radius,
          },
          animatedStyles,
        ]}
      >
        <LinearGradient
          colors={[
            "rgba(255, 255, 255, 0.12)",
            "rgba(255, 255, 255, 0)",
            "rgba(255, 255, 255, 0.12)",
            "rgba(255, 255, 255, 0)",
          ]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          locations={[0, 0.33, 0.66, 1]}
          style={[
            styles.skeletonGradient,
            {
              borderRadius: radius,
            },
          ]}
        />
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  skeletonContainer: {
    overflow: "hidden",
  },
  skeletonGradientContainer: {
    width: "300%",
    height: "100%",
  },
  skeletonGradient: {
    width: "100%",
    height: "100%",
  },
});
