import Svg, { LinearGradient, Defs, Path, Stop } from "react-native-svg";

import { IconProps } from "@/types/Icon";

export default function ChatFolderIcon({
  size = 24,
  color = "#C5C5D3",
  style,
}: IconProps) {
  return (
    <Svg style={style} width={size} height={size} viewBox="0 0 24 24">
      <Defs>
        <LinearGradient
          id="extraction_gradient"
          x1="0%"
          x2="100%"
          y1="0%"
          y2="100%"
        >
          <Stop offset="0%" stopColor="#FD4C21" />
          <Stop offset="100%" stopColor="#B64F72" />
        </LinearGradient>
      </Defs>
      <Path
        fill={color === "gradient" ? "url(#extraction_gradient)" : color}
        d="M12.414 5H21a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h7.414l2 2ZM4 5v14h16V7h-8.414l-2-2H4Zm7 4h2v8h-2V9Zm4 3h2v5h-2v-5Zm-8 2h2v3H7v-3Z"
      />
    </Svg>
  );
}
