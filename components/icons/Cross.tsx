import { Svg, Path } from "react-native-svg";

import { IconProps } from "@/types/Icon";

export default function CrossIcon({ size = 24, color = "#C5C5D3" }: IconProps) {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24">
      <Path
        fill={color}
        d="m12 10.586 4.95-4.95 1.414 1.414-4.95 4.95 4.95 4.95-1.414 1.414-4.95-4.95-4.95 4.95-1.414-1.414 4.95-4.95-4.95-4.95L7.05 5.636l4.95 4.95Z"
      />
    </Svg>
  );
}
