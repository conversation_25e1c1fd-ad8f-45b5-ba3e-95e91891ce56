import Svg, { LinearGradient, Defs, Path, Stop } from "react-native-svg";

import { RateIconProps } from "@/types/Icon";
import Animated, {
  useAnimatedProps,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import { useEffect } from "react";

const AnimatedPath = Animated.createAnimatedComponent(Path);

export default function RateStarIcon({
  size = 24,
  color = "#C5C5D3",
  style,
  isActive = false,
}: RateIconProps) {
  const opacity = useSharedValue(isActive ? 1 : 0);

  useEffect(() => {
    opacity.value = withTiming(isActive ? 1 : 0.12);
  }, [isActive]);

  const animatedProps = useAnimatedProps(() => ({
    opacity: opacity.value,
  }));

  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" style={style}>
      <Path
        fill={color}
        d="m12 17-5.878 3.59 1.598-6.7-5.23-4.48 6.865-.55L12 2.5l2.645 6.36 6.866.55-5.231 4.48 1.598 6.7L12 17Zm0-2.344 2.817 1.72-.766-3.21 2.507-2.147-3.29-.264L12 7.708l-1.268 3.047-3.29.264 2.507 2.147-.766 3.21L12 14.657v-.001Z"
      />
      <Defs>
        <LinearGradient
          id="rate_star_gradient"
          x1="0%"
          x2="100%"
          y1="0%"
          y2="100%"
        >
          <Stop offset="0%" stopColor="#FD4C21" />
          <Stop offset="100%" stopColor="#B64F72" />
        </LinearGradient>
      </Defs>
      <AnimatedPath
        animatedProps={animatedProps}
        fill="url(#rate_star_gradient)"
        d="m12 17-5.878 3.59 1.598-6.7-5.23-4.48 6.865-.55L12 2.5l2.645 6.36 6.866.55-5.231 4.48 1.598 6.7L12 17Z"
      />
    </Svg>
  );
}
