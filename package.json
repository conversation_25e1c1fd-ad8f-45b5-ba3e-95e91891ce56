{"name": "betanal<PERSON>zer", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"dev": "APP_VARIANT=development npx expo start", "start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "postinstall": "patch-package"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo-google-fonts/bai-jamjuree": "^0.3.0", "@expo/vector-icons": "^14.0.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "expo": "^52.0.46", "expo-application": "~6.0.2", "expo-blur": "~14.0.3", "expo-constants": "~17.0.8", "expo-dev-client": "~5.0.20", "expo-document-picker": "~13.0.3", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image": "~2.0.7", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-navigation-bar": "~4.0.9", "expo-router": "~4.0.20", "expo-share-intent": "^3.2.2", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "patch-package": "^8.0.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "~0.77.1", "react-native-gesture-handler": "~2.22.0", "react-native-reanimated": "~3.16.7", "react-native-safe-area-context": "~5.1.0", "react-native-screens": "~4.8.0", "react-native-svg": "15.8.0", "react-native-web": "~0.19.13", "react-native-webview": "~13.13.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true, "expo": {"install": {"exclude": ["react-native@~0.76.6", "react-native-reanimated@~3.16.1", "react-native-gesture-handler@~2.20.0", "react-native-screens@~4.4.0", "react-native-safe-area-context@~4.12.0", "react-native-webview@~13.12.5"]}}}