import OkIcon from "@/components/icons/Ok";
import TrendIcon from "@/components/icons/Trend";
import ErrorIcon from "@/components/icons/Error";
import Speed1Icon from "@/components/icons/Speed1";
import Speed2Icon from "@/components/icons/Speed2";
import Speed3Icon from "@/components/icons/Speed3";
import Speed4Icon from "@/components/icons/Speed4";
import Speed5Icon from "@/components/icons/Speed5";
import WarningIcon from "@/components/icons/Warning";
import DiamondIcon from "@/components/icons/Diamond";

export const getScoreInfo = (score: number | null | undefined) => {
  switch (score) {
    case 1:
      return {
        backgroundColor: "#F24A4C33",
        color: "#F24A4C",
        indicatorColor: "#FF9E9E",
        message:
          "As probabilidades estão claramente contra essa aposta. Todos os principais indicadores são desfavoráveis, e o risco de perda é extremamente alto.",
      };
    case 2:
      return {
        backgroundColor: "#F24A4C33",
        color: "#F24A4C",
        indicatorColor: "#FF9E9E",
        message:
          "A análise indica que essa aposta tem uma chance mínima de sucesso.  Poucos sinais positivos que indicam um desfecho provavelmente não favorável.",
      };
    case 3:
      return {
        backgroundColor: "#FF662E33",
        color: "#FF662E",
        indicatorColor: "#FBC4B0",
        message:
          "Embora tenha potencial, essa aposta apresenta vários sinais de alerta. O risco é significativo, e os dados não sustentam uma previsão otimista. ",
      };
    case 4:
      return {
        backgroundColor: "#FF662E33",
        color: "#FF662E",
        indicatorColor: "#FBC4B0",
        message:
          "Alguns indicadores oferecem uma leve esperança, mas no geral a aposta ainda é arriscada. Existem incertezas importantes que reduzem a confiança.",
      };
    case 5:
      return {
        backgroundColor: "#F8E5411F",
        color: "#F8E541",
        indicatorColor: "#F8F3CE",
        message:
          "Os dados estão equilibrados, sem mostrar uma tendência definida. Há chances reais para ambos os lados tornando o resultado dificil de prever.",
      };
    case 6:
      return {
        backgroundColor: "#F8E5411F",
        color: "#F8E541",
        indicatorColor: "#F8F3CE",
        message:
          "Existem sinais positivos que indicam um leve favoritismo, mas o cenário não é totalmente confiável. Resultado razoável com algum risco.",
      };
    case 7:
      return {
        backgroundColor: "#74D6291F",
        color: "#74D629",
        indicatorColor: "#D6F6BE",
        message:
          "Dados favoráveis indicam boas chances ainda que sem confiança total. Existe algum risco associado, com uma boa perspectiva de sucesso.",
      };
    case 8:
      return {
        backgroundColor: "#74D6291F",
        color: "#74D629",
        indicatorColor: "#D6F6BE",
        message:
          "Com base nos dados estastísticos, essa aposta tem boas chances de sucesso. O risco é baixo, e os dados indicam um elevado potencial de acerto. ",
      };
    case 9:
      return {
        backgroundColor: "#0EA64B1F",
        color: "#0EA64B",
        indicatorColor: "#72EDA3",
        message:
          "Quase todos os indicadores estão alinhados positivamente. O cenário é bem favorável e o risco é muito reduzido. Ideal para quem busca segurança.",
      };
    default:
      return {};
  }
};

export const getExpectedValueInfo = (
  expectedValue: string | null | undefined
) => {
  if (!expectedValue) return {};

  const formattedNumber = Number(expectedValue);

  switch (true) {
    case formattedNumber <= -10:
      return {
        color: "#F24A4C",
        ProbabilityIcon: Speed1Icon,
        ExpectedValueIcon: ErrorIcon,
        evaluation: "Muito baixo",
        reply: "Odds superestimadas, o valor está inflado e sem valor real.",
      };
    case formattedNumber <= -5:
      return {
        color: "#FF662E",
        ProbabilityIcon: Speed2Icon,
        ExpectedValueIcon: WarningIcon,
        evaluation: "Baixo",
        reply: "Odd arriscada, a casa tem vantagem clara sobre você.",
      };
    case formattedNumber <= 0:
      return {
        color: "#F8E541",
        ProbabilityIcon: Speed3Icon,
        ExpectedValueIcon: OkIcon,
        evaluation: "Justo",
        reply: "Odds justas, valor e risco estão bem equilibrados.",
      };
    case formattedNumber <= 1.99:
      return {
        color: "#74D629",
        ProbabilityIcon: Speed4Icon,
        ExpectedValueIcon: TrendIcon,
        evaluation: "Valor bom",
        reply: "Odd em seu favor, vantagem favorável contra a casa.",
      };
    case formattedNumber >= 2:
      return {
        color: "#0EA64B",
        ProbabilityIcon: Speed5Icon,
        ExpectedValueIcon: DiamondIcon,
        evaluation: "Valor muito bom",
        reply: "Probabilidade real supera odds, óptima aposta de valor. ",
      };
    default:
      return {};
  }
};
